#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار توسيط النوافذ
"""

import tkinter as tk
from tkinter import ttk, messagebox

def center_window(window, width=400, height=300):
    """توسيط النافذة في الشاشة"""
    window.update_idletasks()
    x = (window.winfo_screenwidth() // 2) - (width // 2)
    y = (window.winfo_screenheight() // 2) - (height // 2)
    window.geometry(f'{width}x{height}+{x}+{y}')

def test_main_window():
    """اختبار النافذة الرئيسية"""
    root = tk.Tk()
    root.title("اختبار التوسيط - النافذة الرئيسية")
    root.configure(bg='white')
    
    # توسيط النافذة الرئيسية
    center_window(root, 800, 600)
    
    # إضافة محتوى للنافذة
    title_label = tk.Label(root,
                          text="🎯 اختبار توسيط النوافذ",
                          font=('Arial', 18, 'bold'),
                          bg='white',
                          fg='#2c3e50')
    title_label.pack(pady=50)
    
    # زر لفتح نافذة فرعية
    def open_dialog():
        dialog = tk.Toplevel(root)
        dialog.title("نافذة فرعية")
        dialog.configure(bg='white')
        dialog.resizable(False, False)
        dialog.transient(root)
        dialog.grab_set()
        
        # توسيط النافذة الفرعية
        center_window(dialog, 400, 300)
        
        # محتوى النافذة الفرعية
        tk.Label(dialog,
                text="✅ هذه نافذة فرعية في المركز",
                font=('Arial', 14),
                bg='white',
                fg='#27ae60').pack(pady=50)
        
        # أزرار
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(pady=30)
        
        tk.Button(buttons_frame,
                 text="💾 حفظ",
                 bg='#27ae60',
                 fg='white',
                 font=('Arial', 12, 'bold'),
                 relief=tk.FLAT,
                 padx=20,
                 pady=10,
                 command=lambda: messagebox.showinfo("نجح", "تم الحفظ بنجاح!")).pack(side=tk.LEFT, padx=10)
        
        tk.Button(buttons_frame,
                 text="❌ إلغاء",
                 bg='#e74c3c',
                 fg='white',
                 font=('Arial', 12, 'bold'),
                 relief=tk.FLAT,
                 padx=20,
                 pady=10,
                 command=dialog.destroy).pack(side=tk.LEFT, padx=10)
    
    test_btn = tk.Button(root,
                        text="🔍 اختبار نافذة فرعية",
                        command=open_dialog,
                        bg='#3498db',
                        fg='white',
                        font=('Arial', 14, 'bold'),
                        relief=tk.FLAT,
                        padx=30,
                        pady=15)
    test_btn.pack(pady=20)
    
    # معلومات
    info_label = tk.Label(root,
                         text="يجب أن تظهر النوافذ في مركز الشاشة",
                         font=('Arial', 12),
                         bg='white',
                         fg='#7f8c8d')
    info_label.pack(pady=20)
    
    root.mainloop()

if __name__ == "__main__":
    test_main_window()
