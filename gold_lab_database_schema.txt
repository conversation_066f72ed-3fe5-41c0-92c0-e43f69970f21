-- جدول العملاء
CREATE TABLE Customers (
    customer_id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    id_number TEXT UNIQUE NOT NULL,
    phone TEXT,
    address TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جد<PERSON><PERSON> الموظفين
CREATE TABLE Employees (
    employee_id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    id_number TEXT UNIQUE NOT NULL,
    role TEXT NOT NULL, -- مثال: محلل، فني تنقية، فني صب
    phone TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول العينات (لتحليل الذهب)
CREATE TABLE Samples (
    sample_id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    weight REAL NOT NULL, -- الوزن بالغرام
    material_type TEXT NOT NULL, -- نوع المادة (مثل: ذهب خام، سبيكة)
    purity_percentage REAL, -- نسبة النقاء بعد التحليل
    impurities TEXT, -- وصف الشوائب (اختياري)
    analysis_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    analysis_cost REAL, -- تكلفة التحليل
    employee_id INTEGER, -- الموظف الذي أجرى التحليل
    FOREIGN KEY (customer_id) REFERENCES Customers(customer_id),
    FOREIGN KEY (employee_id) REFERENCES Employees(employee_id)
);

-- جدول التنقية
CREATE TABLE Refinements (
    refinement_id INTEGER PRIMARY KEY AUTOINCREMENT,
    sample_id INTEGER NOT NULL,
    raw_weight REAL NOT NULL, -- وزن الذهب الخام قبل التنقية
    refined_weight REAL, -- وزن الذهب بعد التنقية
    loss_weight REAL, -- الخسائر أثناء التنقية
    chemicals_used TEXT, -- المواد الكيميائية المستخدمة
    refinement_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    recovery_percentage REAL, -- نسبة الاسترداد
    employee_id INTEGER, -- الموظف الذي أجرى التنقية
    FOREIGN KEY (sample_id) REFERENCES Samples(sample_id),
    FOREIGN KEY (employee_id) REFERENCES Employees(employee_id)
);

-- جدول الصب
CREATE TABLE Castings (
    casting_id INTEGER PRIMARY KEY AUTOINCREMENT,
    refinement_id INTEGER, -- ربط بعملية التنقية (إن وجدت)
    customer_id INTEGER NOT NULL,
    bar_weight REAL NOT NULL, -- وزن السبيكة
    bar_shape TEXT NOT NULL, -- شكل السبيكة (مثل: مستطيل، دائري)
    purity_percentage REAL NOT NULL, -- نسبة النقاء
    serial_number TEXT UNIQUE NOT NULL, -- رقم تسلسلي فريد للسبيكة
    casting_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    employee_id INTEGER, -- الموظف الذي أجرى الصب
    FOREIGN KEY (refinement_id) REFERENCES Refinements(refinement_id),
    FOREIGN KEY (customer_id) REFERENCES Customers(customer_id),
    FOREIGN KEY (employee_id) REFERENCES Employees(employee_id)
);

-- جدول الفواتير
CREATE TABLE Invoices (
    invoice_id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    sample_id INTEGER, -- ربط بالعينة (اختياري)
    refinement_id INTEGER, -- ربط بعملية التنقية (اختياري)
    casting_id INTEGER, -- ربط بعملية الصب (اختياري)
    total_amount REAL NOT NULL, -- المبلغ الإجمالي
    paid_amount REAL DEFAULT 0, -- المبلغ المدفوع
    status TEXT NOT NULL, -- الحالة (مدفوع، غير مدفوع)
    invoice_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES Customers(customer_id),
    FOREIGN KEY (sample_id) REFERENCES Samples(sample_id),
    FOREIGN KEY (refinement_id) REFERENCES Refinements(refinement_id),
    FOREIGN KEY (casting_id) REFERENCES Castings(casting_id)
);

-- جدول التقارير (اختياري لتخزين التقارير المولدة)
CREATE TABLE Reports (
    report_id INTEGER PRIMARY KEY AUTOINCREMENT,
    report_type TEXT NOT NULL, -- نوع التقرير (يومي، أسبوعي، شهري)
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP NOT NULL,
    total_samples INTEGER, -- عدد العينات المحللة
    total_casted_weight REAL, -- إجمالي وزن السبائك
    total_revenue REAL, -- إجمالي الإيرادات
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);