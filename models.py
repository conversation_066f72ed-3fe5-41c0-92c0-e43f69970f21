from database import DatabaseManager
from datetime import datetime
import sqlite3

class Customer:
    def __init__(self, db_manager):
        self.db = db_manager
    
    def add_customer(self, name, id_number, phone="", address=""):
        """إضافة عميل جديد"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('''
                INSERT INTO Customers (name, id_number, phone, address)
                VALUES (?, ?, ?, ?)
            ''', (name, id_number, phone, address))
            conn.commit()
            customer_id = cursor.lastrowid
            conn.close()
            return customer_id
        except sqlite3.IntegrityError:
            conn.close()
            raise ValueError("رقم الهوية موجود مسبقاً")
    
    def get_all_customers(self):
        """جلب جميع العملاء"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM Customers ORDER BY name')
        customers = cursor.fetchall()
        conn.close()
        return customers
    
    def search_customer(self, search_term):
        """البحث عن عميل بالاسم أو رقم الهوية"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT * FROM Customers 
            WHERE name LIKE ? OR id_number LIKE ?
        ''', (f'%{search_term}%', f'%{search_term}%'))
        customers = cursor.fetchall()
        conn.close()
        return customers
    
    def update_customer(self, customer_id, name, phone, address):
        """تحديث بيانات العميل"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            UPDATE Customers 
            SET name=?, phone=?, address=?
            WHERE customer_id=?
        ''', (name, phone, address, customer_id))
        conn.commit()
        conn.close()

class Employee:
    def __init__(self, db_manager):
        self.db = db_manager
    
    def add_employee(self, name, id_number, role, phone=""):
        """إضافة موظف جديد"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('''
                INSERT INTO Employees (name, id_number, role, phone)
                VALUES (?, ?, ?, ?)
            ''', (name, id_number, role, phone))
            conn.commit()
            employee_id = cursor.lastrowid
            conn.close()
            return employee_id
        except sqlite3.IntegrityError:
            conn.close()
            raise ValueError("رقم الهوية موجود مسبقاً")
    
    def get_all_employees(self):
        """جلب جميع الموظفين"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM Employees ORDER BY name')
        employees = cursor.fetchall()
        conn.close()
        return employees
    
    def get_employees_by_role(self, role):
        """جلب الموظفين حسب الوظيفة"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM Employees WHERE role=?', (role,))
        employees = cursor.fetchall()
        conn.close()
        return employees

class Sample:
    def __init__(self, db_manager):
        self.db = db_manager
    
    def add_sample(self, customer_id, weight, material_type, analysis_cost, employee_id=None):
        """إضافة عينة جديدة"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO Samples (customer_id, weight, material_type, analysis_cost, employee_id)
            VALUES (?, ?, ?, ?, ?)
        ''', (customer_id, weight, material_type, analysis_cost, employee_id))
        conn.commit()
        sample_id = cursor.lastrowid
        conn.close()
        return sample_id
    
    def update_analysis_results(self, sample_id, purity_percentage, impurities=""):
        """تحديث نتائج التحليل"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            UPDATE Samples 
            SET purity_percentage=?, impurities=?
            WHERE sample_id=?
        ''', (purity_percentage, impurities, sample_id))
        conn.commit()
        conn.close()
    
    def get_samples_by_customer(self, customer_id):
        """جلب العينات الخاصة بعميل معين"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT s.*, c.name as customer_name, e.name as employee_name
            FROM Samples s
            LEFT JOIN Customers c ON s.customer_id = c.customer_id
            LEFT JOIN Employees e ON s.employee_id = e.employee_id
            WHERE s.customer_id = ?
            ORDER BY s.analysis_date DESC
        ''', (customer_id,))
        samples = cursor.fetchall()
        conn.close()
        return samples

class Refinement:
    def __init__(self, db_manager):
        self.db = db_manager
    
    def add_refinement(self, sample_id, raw_weight, chemicals_used, employee_id=None):
        """إضافة عملية تنقية جديدة"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO Refinements (sample_id, raw_weight, chemicals_used, employee_id)
            VALUES (?, ?, ?, ?)
        ''', (sample_id, raw_weight, chemicals_used, employee_id))
        conn.commit()
        refinement_id = cursor.lastrowid
        conn.close()
        return refinement_id
    
    def update_refinement_results(self, refinement_id, refined_weight, loss_weight):
        """تحديث نتائج التنقية"""
        recovery_percentage = (refined_weight / (refined_weight + loss_weight)) * 100 if (refined_weight + loss_weight) > 0 else 0
        
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            UPDATE Refinements 
            SET refined_weight=?, loss_weight=?, recovery_percentage=?
            WHERE refinement_id=?
        ''', (refined_weight, loss_weight, recovery_percentage, refinement_id))
        conn.commit()
        conn.close()

class Casting:
    def __init__(self, db_manager):
        self.db = db_manager
    
    def add_casting(self, customer_id, bar_weight, bar_shape, purity_percentage, serial_number, employee_id=None, refinement_id=None):
        """إضافة عملية صب جديدة"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('''
                INSERT INTO Castings (customer_id, bar_weight, bar_shape, purity_percentage, serial_number, employee_id, refinement_id)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (customer_id, bar_weight, bar_shape, purity_percentage, serial_number, employee_id, refinement_id))
            conn.commit()
            casting_id = cursor.lastrowid
            conn.close()
            return casting_id
        except sqlite3.IntegrityError:
            conn.close()
            raise ValueError("الرقم التسلسلي موجود مسبقاً")
    
    def get_castings_by_customer(self, customer_id):
        """جلب السبائك الخاصة بعميل معين"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT c.*, cust.name as customer_name, e.name as employee_name
            FROM Castings c
            LEFT JOIN Customers cust ON c.customer_id = cust.customer_id
            LEFT JOIN Employees e ON c.employee_id = e.employee_id
            WHERE c.customer_id = ?
            ORDER BY c.casting_date DESC
        ''', (customer_id,))
        castings = cursor.fetchall()
        conn.close()
        return castings

class Invoice:
    def __init__(self, db_manager):
        self.db = db_manager
    
    def create_invoice(self, customer_id, total_amount, sample_id=None, refinement_id=None, casting_id=None):
        """إنشاء فاتورة جديدة"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO Invoices (customer_id, total_amount, sample_id, refinement_id, casting_id, status)
            VALUES (?, ?, ?, ?, ?, 'غير مدفوع')
        ''', (customer_id, total_amount, sample_id, refinement_id, casting_id))
        conn.commit()
        invoice_id = cursor.lastrowid
        conn.close()
        return invoice_id
    
    def update_payment(self, invoice_id, paid_amount):
        """تحديث المدفوعات"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        # جلب المبلغ الإجمالي
        cursor.execute('SELECT total_amount FROM Invoices WHERE invoice_id=?', (invoice_id,))
        total_amount = cursor.fetchone()[0]
        
        # تحديث الحالة
        status = 'مدفوع' if paid_amount >= total_amount else 'مدفوع جزئياً'
        
        cursor.execute('''
            UPDATE Invoices 
            SET paid_amount=?, status=?
            WHERE invoice_id=?
        ''', (paid_amount, status, invoice_id))
        conn.commit()
        conn.close()
