import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from models import Customer, Employee, Sample, Refinement, Casting, Invoice
import pandas as pd
from datetime import datetime, timedelta
import os

class ReportsInvoicesManagement:
    def __init__(self, parent_frame, db_manager, colors):
        self.parent_frame = parent_frame
        self.db_manager = db_manager
        self.colors = colors
        
        # إنشاء نماذج البيانات
        self.customer_model = Customer(db_manager)
        self.employee_model = Employee(db_manager)
        self.sample_model = Sample(db_manager)
        self.refinement_model = Refinement(db_manager)
        self.casting_model = Casting(db_manager)
        self.invoice_model = Invoice(db_manager)
        
    def show_reports_management(self):
        """عرض واجهة إدارة التقارير"""
        self.clear_content_frame()
        
        # عنوان الصفحة
        title_frame = tk.Frame(self.parent_frame, bg='white')
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        
        title_label = tk.Label(title_frame,
                              text="📊 التقارير والإحصائيات",
                              font=('Arial', 18, 'bold'),
                              bg='white',
                              fg=self.colors['primary'])
        title_label.pack()
        
        # إطار أزرار التقارير
        reports_frame = tk.Frame(self.parent_frame, bg='white')
        reports_frame.pack(fill=tk.X, padx=20, pady=20)
        
        # تقرير يومي
        daily_btn = tk.Button(reports_frame,
                             text="📅 تقرير يومي",
                             command=self.generate_daily_report,
                             bg=self.colors['secondary'],
                             fg='white',
                             font=('Arial', 12, 'bold'),
                             relief=tk.FLAT,
                             padx=20,
                             pady=15,
                             width=15)
        daily_btn.grid(row=0, column=0, padx=10, pady=10)
        
        # تقرير أسبوعي
        weekly_btn = tk.Button(reports_frame,
                              text="📆 تقرير أسبوعي",
                              command=self.generate_weekly_report,
                              bg=self.colors['success'],
                              fg='white',
                              font=('Arial', 12, 'bold'),
                              relief=tk.FLAT,
                              padx=20,
                              pady=15,
                              width=15)
        weekly_btn.grid(row=0, column=1, padx=10, pady=10)
        
        # تقرير شهري
        monthly_btn = tk.Button(reports_frame,
                               text="📊 تقرير شهري",
                               command=self.generate_monthly_report,
                               bg=self.colors['warning'],
                               fg='white',
                               font=('Arial', 12, 'bold'),
                               relief=tk.FLAT,
                               padx=20,
                               pady=15,
                               width=15)
        monthly_btn.grid(row=0, column=2, padx=10, pady=10)
        
        # تقرير مخصص
        custom_btn = tk.Button(reports_frame,
                              text="🔧 تقرير مخصص",
                              command=self.show_custom_report_dialog,
                              bg=self.colors['danger'],
                              fg='white',
                              font=('Arial', 12, 'bold'),
                              relief=tk.FLAT,
                              padx=20,
                              pady=15,
                              width=15)
        custom_btn.grid(row=0, column=3, padx=10, pady=10)
        
        # إطار عرض التقرير
        self.report_display_frame = tk.Frame(self.parent_frame, bg='white')
        self.report_display_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # رسالة ترحيب
        welcome_label = tk.Label(self.report_display_frame,
                                text="اختر نوع التقرير المطلوب من الأزرار أعلاه",
                                font=('Arial', 14),
                                bg='white',
                                fg=self.colors['dark'])
        welcome_label.pack(expand=True)
    
    def show_invoices_management(self):
        """عرض واجهة إدارة الفواتير"""
        self.clear_content_frame()
        
        # عنوان الصفحة
        title_frame = tk.Frame(self.parent_frame, bg='white')
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        
        title_label = tk.Label(title_frame,
                              text="📄 إدارة الفواتير",
                              font=('Arial', 18, 'bold'),
                              bg='white',
                              fg=self.colors['primary'])
        title_label.pack(side=tk.LEFT)
        
        # زر إنشاء فاتورة جديدة
        create_btn = tk.Button(title_frame,
                              text="➕ إنشاء فاتورة",
                              command=self.show_create_invoice_dialog,
                              bg=self.colors['success'],
                              fg='white',
                              font=('Arial', 10, 'bold'),
                              relief=tk.FLAT,
                              padx=15,
                              pady=5)
        create_btn.pack(side=tk.RIGHT)
        
        # جدول الفواتير
        self.create_invoices_table()
        self.load_invoices()
    
    def clear_content_frame(self):
        """مسح محتوى الإطار"""
        for widget in self.parent_frame.winfo_children():
            widget.destroy()
    
    def generate_daily_report(self):
        """إنتاج تقرير يومي"""
        today = datetime.now().date()
        self.generate_report(today, today, "يومي")
    
    def generate_weekly_report(self):
        """إنتاج تقرير أسبوعي"""
        today = datetime.now().date()
        week_start = today - timedelta(days=today.weekday())
        self.generate_report(week_start, today, "أسبوعي")
    
    def generate_monthly_report(self):
        """إنتاج تقرير شهري"""
        today = datetime.now().date()
        month_start = today.replace(day=1)
        self.generate_report(month_start, today, "شهري")
    
    def generate_report(self, start_date, end_date, report_type):
        """إنتاج التقرير"""
        # مسح الإطار الحالي
        for widget in self.report_display_frame.winfo_children():
            widget.destroy()
        
        # عنوان التقرير
        report_title = tk.Label(self.report_display_frame,
                               text=f"التقرير ال{report_type} من {start_date} إلى {end_date}",
                               font=('Arial', 16, 'bold'),
                               bg='white',
                               fg=self.colors['primary'])
        report_title.pack(pady=20)
        
        # جلب البيانات
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        # إحصائيات العينات
        cursor.execute('''
            SELECT COUNT(*), AVG(weight), SUM(analysis_cost)
            FROM Samples 
            WHERE DATE(analysis_date) BETWEEN ? AND ?
        ''', (start_date, end_date))
        samples_stats = cursor.fetchone()
        
        # إحصائيات السبائك
        cursor.execute('''
            SELECT COUNT(*), SUM(bar_weight), AVG(purity_percentage)
            FROM Castings 
            WHERE DATE(casting_date) BETWEEN ? AND ?
        ''', (start_date, end_date))
        castings_stats = cursor.fetchone()
        
        # إحصائيات الفواتير
        cursor.execute('''
            SELECT COUNT(*), SUM(total_amount), SUM(paid_amount)
            FROM Invoices 
            WHERE DATE(invoice_date) BETWEEN ? AND ?
        ''', (start_date, end_date))
        invoices_stats = cursor.fetchone()
        
        conn.close()
        
        # إطار الإحصائيات
        stats_frame = tk.Frame(self.report_display_frame, bg='white')
        stats_frame.pack(fill=tk.X, padx=20, pady=20)
        
        # بطاقات الإحصائيات
        self.create_report_card(stats_frame, "🔬 العينات المحللة", 
                               f"{samples_stats[0] or 0}", 
                               f"متوسط الوزن: {samples_stats[1]:.2f} غ" if samples_stats[1] else "متوسط الوزن: 0 غ",
                               self.colors['secondary'], 0, 0)
        
        self.create_report_card(stats_frame, "🏭 السبائك المصبوبة", 
                               f"{castings_stats[0] or 0}", 
                               f"إجمالي الوزن: {castings_stats[1]:.2f} غ" if castings_stats[1] else "إجمالي الوزن: 0 غ",
                               self.colors['gold'], 0, 1)
        
        self.create_report_card(stats_frame, "💰 إجمالي الإيرادات", 
                               f"{invoices_stats[1]:.2f} ريال" if invoices_stats[1] else "0 ريال", 
                               f"المدفوع: {invoices_stats[2]:.2f} ريال" if invoices_stats[2] else "المدفوع: 0 ريال",
                               self.colors['success'], 0, 2)
        
        # زر تصدير التقرير
        export_frame = tk.Frame(self.report_display_frame, bg='white')
        export_frame.pack(pady=20)
        
        export_btn = tk.Button(export_frame,
                              text="📤 تصدير التقرير إلى Excel",
                              command=lambda: self.export_report_to_excel(start_date, end_date, report_type),
                              bg=self.colors['warning'],
                              fg='white',
                              font=('Arial', 12, 'bold'),
                              relief=tk.FLAT,
                              padx=20,
                              pady=10)
        export_btn.pack()
    
    def create_report_card(self, parent, title, value, subtitle, color, row, col):
        """إنشاء بطاقة إحصائية للتقرير"""
        card_frame = tk.Frame(parent, bg=color, relief=tk.RAISED, bd=2)
        card_frame.grid(row=row, column=col, padx=15, pady=10, sticky='ew')
        
        parent.grid_columnconfigure(col, weight=1)
        
        # العنوان
        title_label = tk.Label(card_frame,
                              text=title,
                              font=('Arial', 12, 'bold'),
                              bg=color,
                              fg='white')
        title_label.pack(pady=(15, 5))
        
        # القيمة
        value_label = tk.Label(card_frame,
                              text=value,
                              font=('Arial', 16, 'bold'),
                              bg=color,
                              fg='white')
        value_label.pack(pady=5)
        
        # العنوان الفرعي
        subtitle_label = tk.Label(card_frame,
                                 text=subtitle,
                                 font=('Arial', 10),
                                 bg=color,
                                 fg='white')
        subtitle_label.pack(pady=(5, 15))
    
    def show_custom_report_dialog(self):
        """عرض نافذة التقرير المخصص"""
        dialog = tk.Toplevel(self.parent_frame)
        dialog.title("تقرير مخصص")
        dialog.geometry("450x400")
        dialog.configure(bg='white')
        dialog.resizable(False, False)
        dialog.transient(self.parent_frame)
        dialog.grab_set()
        
        # عنوان النافذة
        title_label = tk.Label(dialog,
                              text="🔧 إنشاء تقرير مخصص",
                              font=('Arial', 16, 'bold'),
                              bg='white',
                              fg=self.colors['primary'])
        title_label.pack(pady=20)
        
        # إطار النموذج
        form_frame = tk.Frame(dialog, bg='white')
        form_frame.pack(padx=30, pady=20, fill=tk.BOTH, expand=True)
        
        # تاريخ البداية
        tk.Label(form_frame, text="تاريخ البداية:", font=('Arial', 12), bg='white').grid(row=0, column=0, sticky='w', pady=10)
        start_date_entry = tk.Entry(form_frame, font=('Arial', 12), width=25)
        start_date_entry.grid(row=0, column=1, padx=(10, 0), pady=10)
        start_date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
        
        # تاريخ النهاية
        tk.Label(form_frame, text="تاريخ النهاية:", font=('Arial', 12), bg='white').grid(row=1, column=0, sticky='w', pady=10)
        end_date_entry = tk.Entry(form_frame, font=('Arial', 12), width=25)
        end_date_entry.grid(row=1, column=1, padx=(10, 0), pady=10)
        end_date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
        
        # أزرار العمليات
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(pady=20)
        
        generate_btn = tk.Button(buttons_frame,
                                text="📊 إنتاج التقرير",
                                command=lambda: self.generate_custom_report(start_date_entry.get(), end_date_entry.get(), dialog),
                                bg=self.colors['success'],
                                fg='white',
                                font=('Arial', 12, 'bold'),
                                relief=tk.FLAT,
                                padx=20,
                                pady=10)
        generate_btn.pack(side=tk.LEFT, padx=10)
        
        cancel_btn = tk.Button(buttons_frame,
                              text="❌ إلغاء",
                              command=dialog.destroy,
                              bg=self.colors['danger'],
                              fg='white',
                              font=('Arial', 12, 'bold'),
                              relief=tk.FLAT,
                              padx=20,
                              pady=10)
        cancel_btn.pack(side=tk.LEFT, padx=10)
    
    def generate_custom_report(self, start_date_str, end_date_str, dialog):
        """إنتاج التقرير المخصص"""
        try:
            start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
            end_date = datetime.strptime(end_date_str, "%Y-%m-%d").date()
            
            dialog.destroy()
            self.generate_report(start_date, end_date, "مخصص")
            
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال التواريخ بالصيغة الصحيحة (YYYY-MM-DD)")
    
    def export_report_to_excel(self, start_date, end_date, report_type):
        """تصدير التقرير إلى Excel"""
        try:
            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="حفظ التقرير"
            )
            
            if not filename:
                return
            
            # جلب البيانات
            conn = self.db_manager.get_connection()
            
            # بيانات العينات
            samples_df = pd.read_sql_query('''
                SELECT s.sample_id as "رقم العينة", c.name as "العميل", s.weight as "الوزن", 
                       s.material_type as "نوع المادة", s.purity_percentage as "نسبة النقاء",
                       s.analysis_cost as "تكلفة التحليل", s.analysis_date as "تاريخ التحليل"
                FROM Samples s
                LEFT JOIN Customers c ON s.customer_id = c.customer_id
                WHERE DATE(s.analysis_date) BETWEEN ? AND ?
            ''', conn, params=(start_date, end_date))
            
            # بيانات السبائك
            castings_df = pd.read_sql_query('''
                SELECT c.casting_id as "رقم السبيكة", cust.name as "العميل", c.bar_weight as "الوزن",
                       c.bar_shape as "الشكل", c.purity_percentage as "نسبة النقاء",
                       c.serial_number as "الرقم التسلسلي", c.casting_date as "تاريخ الصب"
                FROM Castings c
                LEFT JOIN Customers cust ON c.customer_id = cust.customer_id
                WHERE DATE(c.casting_date) BETWEEN ? AND ?
            ''', conn, params=(start_date, end_date))
            
            conn.close()
            
            # إنشاء ملف Excel
            with pd.ExcelWriter(filename, engine='xlsxwriter') as writer:
                samples_df.to_excel(writer, sheet_name='العينات', index=False)
                castings_df.to_excel(writer, sheet_name='السبائك', index=False)
            
            messagebox.showinfo("نجح", f"تم تصدير التقرير بنجاح إلى:\n{filename}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")
    
    def create_invoices_table(self):
        """إنشاء جدول الفواتير"""
        table_frame = tk.Frame(self.parent_frame, bg='white')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        columns = ('رقم الفاتورة', 'العميل', 'المبلغ الإجمالي', 'المبلغ المدفوع', 'الحالة', 'التاريخ')
        self.invoices_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=12)
        
        for col in columns:
            self.invoices_tree.heading(col, text=col)
            self.invoices_tree.column(col, width=150, anchor='center')
        
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.invoices_tree.yview)
        self.invoices_tree.configure(yscrollcommand=scrollbar.set)
        
        self.invoices_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط حدث النقر المزدوج لتحديث المدفوعات
        self.invoices_tree.bind('<Double-1>', self.update_payment)
    
    def load_invoices(self):
        """تحميل بيانات الفواتير"""
        for item in self.invoices_tree.get_children():
            self.invoices_tree.delete(item)
        
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT i.invoice_id, c.name, i.total_amount, i.paid_amount, i.status, i.invoice_date
            FROM Invoices i
            LEFT JOIN Customers c ON i.customer_id = c.customer_id
            ORDER BY i.invoice_date DESC
        ''')
        invoices = cursor.fetchall()
        conn.close()
        
        for invoice in invoices:
            self.invoices_tree.insert('', tk.END, values=invoice)
    
    def show_create_invoice_dialog(self):
        """عرض نافذة إنشاء فاتورة جديدة"""
        dialog = tk.Toplevel(self.parent_frame)
        dialog.title("إنشاء فاتورة جديدة")
        dialog.geometry("450x400")
        dialog.configure(bg='white')
        dialog.resizable(False, False)
        dialog.transient(self.parent_frame)
        dialog.grab_set()
        
        # عنوان النافذة
        title_label = tk.Label(dialog,
                              text="📄 إنشاء فاتورة جديدة",
                              font=('Arial', 16, 'bold'),
                              bg='white',
                              fg=self.colors['primary'])
        title_label.pack(pady=20)
        
        # إطار النموذج
        form_frame = tk.Frame(dialog, bg='white')
        form_frame.pack(padx=30, pady=20, fill=tk.BOTH, expand=True)
        
        # اختيار العميل
        tk.Label(form_frame, text="العميل:", font=('Arial', 12), bg='white').grid(row=0, column=0, sticky='w', pady=10)
        customers = self.customer_model.get_all_customers()
        customer_names = [f"{c[1]} - {c[2]}" for c in customers]
        customer_combo = ttk.Combobox(form_frame, values=customer_names, state='readonly', width=25)
        customer_combo.grid(row=0, column=1, padx=(10, 0), pady=10)
        
        # المبلغ الإجمالي
        tk.Label(form_frame, text="المبلغ الإجمالي:", font=('Arial', 12), bg='white').grid(row=1, column=0, sticky='w', pady=10)
        amount_entry = tk.Entry(form_frame, font=('Arial', 12), width=27)
        amount_entry.grid(row=1, column=1, padx=(10, 0), pady=10)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(pady=20)
        
        create_btn = tk.Button(buttons_frame,
                              text="💾 إنشاء الفاتورة",
                              command=lambda: self.create_invoice(customer_combo, amount_entry, customers, dialog),
                              bg=self.colors['success'],
                              fg='white',
                              font=('Arial', 12, 'bold'),
                              relief=tk.FLAT,
                              padx=20,
                              pady=10)
        create_btn.pack(side=tk.LEFT, padx=10)
        
        cancel_btn = tk.Button(buttons_frame,
                              text="❌ إلغاء",
                              command=dialog.destroy,
                              bg=self.colors['danger'],
                              fg='white',
                              font=('Arial', 12, 'bold'),
                              relief=tk.FLAT,
                              padx=20,
                              pady=10)
        cancel_btn.pack(side=tk.LEFT, padx=10)
    
    def create_invoice(self, customer_combo, amount_entry, customers, dialog):
        """إنشاء فاتورة جديدة"""
        try:
            customer_index = customer_combo.current()
            total_amount = float(amount_entry.get())
            
            if customer_index == -1:
                messagebox.showerror("خطأ", "يرجى اختيار عميل")
                return
            
            customer_id = customers[customer_index][0]
            
            invoice_id = self.invoice_model.create_invoice(customer_id, total_amount)
            
            messagebox.showinfo("نجح", "تم إنشاء الفاتورة بنجاح")
            dialog.destroy()
            self.load_invoices()
            
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيمة صحيحة للمبلغ")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")
    
    def update_payment(self, event):
        """تحديث المدفوعات"""
        selection = self.invoices_tree.selection()
        if not selection:
            return
        
        item = self.invoices_tree.item(selection[0])
        invoice_data = item['values']
        invoice_id = invoice_data[0]
        total_amount = invoice_data[2]
        current_paid = invoice_data[3]
        
        # نافذة تحديث المدفوعات
        dialog = tk.Toplevel(self.parent_frame)
        dialog.title("تحديث المدفوعات")
        dialog.geometry("400x300")
        dialog.configure(bg='white')
        dialog.resizable(False, False)
        dialog.transient(self.parent_frame)
        dialog.grab_set()
        
        # عنوان النافذة
        title_label = tk.Label(dialog,
                              text="💰 تحديث المدفوعات",
                              font=('Arial', 16, 'bold'),
                              bg='white',
                              fg=self.colors['primary'])
        title_label.pack(pady=20)
        
        # إطار النموذج
        form_frame = tk.Frame(dialog, bg='white')
        form_frame.pack(padx=30, pady=20, fill=tk.BOTH, expand=True)
        
        # عرض المعلومات
        tk.Label(form_frame, text=f"المبلغ الإجمالي: {total_amount}", font=('Arial', 12), bg='white').pack(pady=5)
        tk.Label(form_frame, text=f"المدفوع حالياً: {current_paid}", font=('Arial', 12), bg='white').pack(pady=5)
        
        # المبلغ المدفوع الجديد
        tk.Label(form_frame, text="المبلغ المدفوع:", font=('Arial', 12), bg='white').pack(pady=(10, 5))
        paid_entry = tk.Entry(form_frame, font=('Arial', 12), width=20)
        paid_entry.pack(pady=5)
        paid_entry.insert(0, str(current_paid))
        
        # أزرار العمليات
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(pady=20)
        
        save_btn = tk.Button(buttons_frame,
                            text="💾 حفظ",
                            command=lambda: self.save_payment_update(invoice_id, paid_entry.get(), dialog),
                            bg=self.colors['success'],
                            fg='white',
                            font=('Arial', 12, 'bold'),
                            relief=tk.FLAT,
                            padx=20,
                            pady=10)
        save_btn.pack(side=tk.LEFT, padx=10)
        
        cancel_btn = tk.Button(buttons_frame,
                              text="❌ إلغاء",
                              command=dialog.destroy,
                              bg=self.colors['danger'],
                              fg='white',
                              font=('Arial', 12, 'bold'),
                              relief=tk.FLAT,
                              padx=20,
                              pady=10)
        cancel_btn.pack(side=tk.LEFT, padx=10)
    
    def save_payment_update(self, invoice_id, paid_amount_str, dialog):
        """حفظ تحديث المدفوعات"""
        try:
            paid_amount = float(paid_amount_str)
            
            self.invoice_model.update_payment(invoice_id, paid_amount)
            
            messagebox.showinfo("نجح", "تم تحديث المدفوعات بنجاح")
            dialog.destroy()
            self.load_invoices()
            
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيمة صحيحة للمبلغ المدفوع")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")
