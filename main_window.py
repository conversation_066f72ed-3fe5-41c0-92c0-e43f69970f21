import tkinter as tk
from tkinter import ttk, messagebox
import os
from database import DatabaseManager
from models import Customer, Employee, Sample, Refinement, Casting, Invoice
from customer_management import CustomerManagement
from employee_management import EmployeeManagement
from operations_management import OperationsManagement
from reports_invoices import ReportsInvoicesManagement

class GoldLabApp:
    def __init__(self, root, db_manager):
        self.root = root
        self.root.title("نظام إدارة معمل تحليل وصب الذهب")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')

        # إعداد قاعدة البيانات
        self.db_manager = db_manager
        self.customer_model = Customer(self.db_manager)
        self.employee_model = Employee(self.db_manager)
        self.sample_model = Sample(self.db_manager)
        self.refinement_model = Refinement(self.db_manager)
        self.casting_model = Casting(self.db_manager)
        self.invoice_model = Invoice(self.db_manager)

        # إنشاء مديري العملاء والموظفين والعمليات والتقارير
        self.customer_manager = None
        self.employee_manager = None
        self.operations_manager = None
        self.reports_manager = None

        self.setup_styles()
        self.create_main_interface()
        
    def setup_styles(self):
        """إعداد الأنماط والألوان"""
        self.colors = {
            'primary': '#2c3e50',
            'secondary': '#3498db',
            'success': '#27ae60',
            'warning': '#f39c12',
            'danger': '#e74c3c',
            'light': '#ecf0f1',
            'dark': '#34495e',
            'gold': '#f1c40f',
            'white': '#ffffff',
            'gray': '#95a5a6'
        }
        
        style = ttk.Style()
        style.theme_use('clam')
        
        # تخصيص الأنماط
        style.configure('Title.TLabel', 
                       font=('Arial', 16, 'bold'),
                       background=self.colors['primary'],
                       foreground='white')
        
        style.configure('Header.TLabel',
                       font=('Arial', 12, 'bold'),
                       background=self.colors['light'])
        
        style.configure('Custom.TButton',
                       font=('Arial', 10),
                       padding=10)
        
    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية"""
        # إنشاء الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg=self.colors['light'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # شريط العنوان
        title_frame = tk.Frame(main_frame, bg=self.colors['primary'], height=80)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, 
                              text="🏆 نظام إدارة معمل تحليل وصب الذهب 🏆",
                              font=('Arial', 20, 'bold'),
                              bg=self.colors['primary'],
                              fg='white')
        title_label.pack(expand=True)
        
        # إنشاء الإطار الجانبي للقائمة
        sidebar_frame = tk.Frame(main_frame, bg=self.colors['dark'], width=250)
        sidebar_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        sidebar_frame.pack_propagate(False)
        
        # إنشاء الإطار الرئيسي للمحتوى
        self.content_frame = tk.Frame(main_frame, bg='white')
        self.content_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        self.create_sidebar_menu(sidebar_frame)
        self.show_dashboard()
        
    def create_sidebar_menu(self, parent):
        """إنشاء القائمة الجانبية"""
        # عنوان القائمة
        menu_title = tk.Label(parent, 
                             text="القائمة الرئيسية",
                             font=('Arial', 14, 'bold'),
                             bg=self.colors['dark'],
                             fg='white',
                             pady=20)
        menu_title.pack(fill=tk.X)
        
        # أزرار القائمة
        menu_buttons = [
            ("🏠 لوحة التحكم", self.show_dashboard),
            ("👥 إدارة العملاء", self.show_customers),
            ("👨‍💼 إدارة الموظفين", self.show_employees),
            ("🔬 التحليل", self.show_analysis),
            ("⚗️ التنقية", self.show_refinement),
            ("🏭 الصب", self.show_casting),
            ("📄 الفواتير", self.show_invoices),
            ("📊 التقارير", self.show_reports),
            ("⚙️ الإعدادات", self.show_settings)
        ]
        
        for text, command in menu_buttons:
            btn = tk.Button(parent,
                           text=text,
                           command=command,
                           font=('Arial', 11),
                           bg=self.colors['secondary'],
                           fg='white',
                           relief=tk.FLAT,
                           pady=15,
                           cursor='hand2')
            btn.pack(fill=tk.X, padx=10, pady=2)
            
            # تأثير hover
            btn.bind("<Enter>", lambda e, b=btn: b.configure(bg=self.colors['gold']))
            btn.bind("<Leave>", lambda e, b=btn: b.configure(bg=self.colors['secondary']))
    
    def clear_content_frame(self):
        """مسح محتوى الإطار الرئيسي"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def show_dashboard(self):
        """عرض لوحة التحكم"""
        self.clear_content_frame()
        
        # عنوان لوحة التحكم
        title = tk.Label(self.content_frame,
                        text="لوحة التحكم",
                        font=('Arial', 18, 'bold'),
                        bg='white',
                        fg=self.colors['primary'])
        title.pack(pady=20)
        
        # إنشاء بطاقات الإحصائيات
        stats_frame = tk.Frame(self.content_frame, bg='white')
        stats_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # بطاقة العملاء
        self.create_stat_card(stats_frame, "👥", "العملاء", "0", self.colors['secondary'], 0, 0)
        
        # بطاقة الموظفين
        self.create_stat_card(stats_frame, "👨‍💼", "الموظفين", "0", self.colors['success'], 0, 1)
        
        # بطاقة العينات
        self.create_stat_card(stats_frame, "🔬", "العينات", "0", self.colors['warning'], 1, 0)
        
        # بطاقة السبائك
        self.create_stat_card(stats_frame, "🏭", "السبائك", "0", self.colors['danger'], 1, 1)
        
        # تحديث الإحصائيات
        self.update_dashboard_stats()
        
        # رسالة ترحيب
        welcome_frame = tk.Frame(self.content_frame, bg='white')
        welcome_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        welcome_text = tk.Text(welcome_frame,
                              font=('Arial', 12),
                              bg=self.colors['light'],
                              relief=tk.FLAT,
                              wrap=tk.WORD,
                              height=10)
        welcome_text.pack(fill=tk.BOTH, expand=True)
        
        welcome_content = """
مرحباً بك في نظام إدارة معمل تحليل وصب الذهب

هذا النظام يوفر لك:
• إدارة شاملة لبيانات العملاء والموظفين
• تتبع عمليات التحليل والتنقية والصب
• إنشاء الفواتير والتقارير المالية
• تقارير مفصلة عن الأداء والإنتاجية

للبدء، اختر أحد الخيارات من القائمة الجانبية.
        """
        
        welcome_text.insert(tk.END, welcome_content)
        welcome_text.configure(state=tk.DISABLED)
    
    def create_stat_card(self, parent, icon, title, value, color, row, col):
        """إنشاء بطاقة إحصائية"""
        card_frame = tk.Frame(parent, bg=color, relief=tk.RAISED, bd=2)
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky='ew')
        
        parent.grid_columnconfigure(col, weight=1)
        
        # الأيقونة
        icon_label = tk.Label(card_frame,
                             text=icon,
                             font=('Arial', 24),
                             bg=color,
                             fg='white')
        icon_label.pack(pady=(10, 5))
        
        # العنوان
        title_label = tk.Label(card_frame,
                              text=title,
                              font=('Arial', 12, 'bold'),
                              bg=color,
                              fg='white')
        title_label.pack()
        
        # القيمة
        value_label = tk.Label(card_frame,
                              text=value,
                              font=('Arial', 16, 'bold'),
                              bg=color,
                              fg='white')
        value_label.pack(pady=(5, 10))
        
        # حفظ مرجع للتحديث لاحقاً
        setattr(self, f"{title}_value_label", value_label)
    
    def update_dashboard_stats(self):
        """تحديث إحصائيات لوحة التحكم"""
        try:
            # عدد العملاء
            customers = self.customer_model.get_all_customers()
            if hasattr(self, 'العملاء_value_label'):
                self.العملاء_value_label.configure(text=str(len(customers)))
            
            # عدد الموظفين
            employees = self.employee_model.get_all_employees()
            if hasattr(self, 'الموظفين_value_label'):
                self.الموظفين_value_label.configure(text=str(len(employees)))
                
        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")
    
    # دوال عرض النوافذ المختلفة (سيتم تطويرها لاحقاً)
    def show_customers(self):
        self.clear_content_frame()
        self.customer_manager = CustomerManagement(self.content_frame, self.db_manager, self.colors)
        self.customer_manager.show_customer_management()
    
    def show_employees(self):
        self.clear_content_frame()
        self.employee_manager = EmployeeManagement(self.content_frame, self.db_manager, self.colors)
        self.employee_manager.show_employee_management()
    
    def show_analysis(self):
        self.clear_content_frame()
        self.operations_manager = OperationsManagement(self.content_frame, self.db_manager, self.colors)
        self.operations_manager.show_analysis_management()

    def show_refinement(self):
        self.clear_content_frame()
        self.operations_manager = OperationsManagement(self.content_frame, self.db_manager, self.colors)
        self.operations_manager.show_refinement_management()

    def show_casting(self):
        self.clear_content_frame()
        self.operations_manager = OperationsManagement(self.content_frame, self.db_manager, self.colors)
        self.operations_manager.show_casting_management()
    
    def show_invoices(self):
        self.clear_content_frame()
        self.reports_manager = ReportsInvoicesManagement(self.content_frame, self.db_manager, self.colors)
        self.reports_manager.show_invoices_management()
    
    def show_reports(self):
        self.clear_content_frame()
        self.reports_manager = ReportsInvoicesManagement(self.content_frame, self.db_manager, self.colors)
        self.reports_manager.show_reports_management()
    
    def show_settings(self):
        self.clear_content_frame()
        title = tk.Label(self.content_frame, text="الإعدادات", font=('Arial', 18, 'bold'))
        title.pack(pady=20)
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    from database import DatabaseManager
    root = tk.Tk()
    db_manager = DatabaseManager()
    app = GoldLabApp(root, db_manager)
    app.run()
