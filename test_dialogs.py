#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النوافذ المنبثقة للتأكد من ظهور الأزرار
"""

import tkinter as tk
from tkinter import ttk, messagebox
from database import DatabaseManager
from customer_management import CustomerManagement
from employee_management import EmployeeManagement

def test_customer_dialog():
    """اختبار نافذة إضافة العملاء"""
    root = tk.Tk()
    root.geometry("800x600")

    # توسيط النافذة الرئيسية
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')

    root.withdraw()  # إخفاء النافذة الرئيسية
    
    db_manager = DatabaseManager()
    colors = {
        'primary': '#2c3e50',
        'secondary': '#3498db',
        'success': '#27ae60',
        'warning': '#f39c12',
        'danger': '#e74c3c',
        'light': '#ecf0f1',
        'dark': '#34495e',
        'gold': '#f1c40f'
    }
    
    customer_mgmt = CustomerManagement(root, db_manager, colors)
    customer_mgmt.show_add_customer_dialog()
    
    root.mainloop()

def test_employee_dialog():
    """اختبار نافذة إضافة الموظفين"""
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    db_manager = DatabaseManager()
    colors = {
        'primary': '#2c3e50',
        'secondary': '#3498db',
        'success': '#27ae60',
        'warning': '#f39c12',
        'danger': '#e74c3c',
        'light': '#ecf0f1',
        'dark': '#34495e',
        'gold': '#f1c40f'
    }
    
    employee_mgmt = EmployeeManagement(root, db_manager, colors)
    employee_mgmt.show_add_employee_dialog()
    
    root.mainloop()

if __name__ == "__main__":
    print("اختبار النوافذ المنبثقة...")
    print("1. اختبار نافذة العملاء")
    print("2. اختبار نافذة الموظفين")
    
    choice = input("اختر رقم الاختبار (1 أو 2): ")
    
    if choice == "1":
        test_customer_dialog()
    elif choice == "2":
        test_employee_dialog()
    else:
        print("اختيار غير صحيح")
