import tkinter as tk
from tkinter import ttk, messagebox
from models import Customer, Employee, <PERSON><PERSON>, Refinement, Casting
import random
import string
from datetime import datetime

class OperationsManagement:
    def __init__(self, parent_frame, db_manager, colors):
        self.parent_frame = parent_frame
        self.db_manager = db_manager
        self.colors = colors
        
        # إنشاء نماذج البيانات
        self.customer_model = Customer(db_manager)
        self.employee_model = Employee(db_manager)
        self.sample_model = Sample(db_manager)
        self.refinement_model = Refinement(db_manager)
        self.casting_model = Casting(db_manager)
        
    def show_analysis_management(self):
        """عرض واجهة إدارة التحليل"""
        self.clear_content_frame()
        
        # عنوان الصفحة
        title_frame = tk.Frame(self.parent_frame, bg='white')
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        
        title_label = tk.Label(title_frame,
                              text="🔬 إدارة التحليل",
                              font=('Arial', 18, 'bold'),
                              bg='white',
                              fg=self.colors['primary'])
        title_label.pack(side=tk.LEFT)
        
        # زر إضافة عينة جديدة
        add_btn = tk.Button(title_frame,
                           text="➕ إضافة عينة للتحليل",
                           command=self.show_add_sample_dialog,
                           bg=self.colors['success'],
                           fg='white',
                           font=('Arial', 10, 'bold'),
                           relief=tk.FLAT,
                           padx=15,
                           pady=5)
        add_btn.pack(side=tk.RIGHT)
        
        # جدول العينات
        self.create_samples_table()
        self.load_samples()
    
    def show_refinement_management(self):
        """عرض واجهة إدارة التنقية"""
        self.clear_content_frame()
        
        # عنوان الصفحة
        title_frame = tk.Frame(self.parent_frame, bg='white')
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        
        title_label = tk.Label(title_frame,
                              text="⚗️ إدارة التنقية",
                              font=('Arial', 18, 'bold'),
                              bg='white',
                              fg=self.colors['primary'])
        title_label.pack(side=tk.LEFT)
        
        # زر إضافة عملية تنقية
        add_btn = tk.Button(title_frame,
                           text="➕ إضافة عملية تنقية",
                           command=self.show_add_refinement_dialog,
                           bg=self.colors['warning'],
                           fg='white',
                           font=('Arial', 10, 'bold'),
                           relief=tk.FLAT,
                           padx=15,
                           pady=5)
        add_btn.pack(side=tk.RIGHT)
        
        # جدول عمليات التنقية
        self.create_refinements_table()
        self.load_refinements()
    
    def show_casting_management(self):
        """عرض واجهة إدارة الصب"""
        self.clear_content_frame()
        
        # عنوان الصفحة
        title_frame = tk.Frame(self.parent_frame, bg='white')
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        
        title_label = tk.Label(title_frame,
                              text="🏭 إدارة الصب",
                              font=('Arial', 18, 'bold'),
                              bg='white',
                              fg=self.colors['primary'])
        title_label.pack(side=tk.LEFT)
        
        # زر إضافة عملية صب
        add_btn = tk.Button(title_frame,
                           text="➕ إضافة عملية صب",
                           command=self.show_add_casting_dialog,
                           bg=self.colors['gold'],
                           fg='white',
                           font=('Arial', 10, 'bold'),
                           relief=tk.FLAT,
                           padx=15,
                           pady=5)
        add_btn.pack(side=tk.RIGHT)
        
        # جدول عمليات الصب
        self.create_castings_table()
        self.load_castings()
    
    def clear_content_frame(self):
        """مسح محتوى الإطار"""
        for widget in self.parent_frame.winfo_children():
            widget.destroy()
    
    def create_samples_table(self):
        """إنشاء جدول العينات"""
        table_frame = tk.Frame(self.parent_frame, bg='white')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        columns = ('ID', 'العميل', 'الوزن (غ)', 'نوع المادة', 'نسبة النقاء %', 'التكلفة', 'الموظف', 'التاريخ')
        self.samples_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=12)
        
        for col in columns:
            self.samples_tree.heading(col, text=col)
            self.samples_tree.column(col, width=120, anchor='center')
        
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.samples_tree.yview)
        self.samples_tree.configure(yscrollcommand=scrollbar.set)
        
        self.samples_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط حدث النقر المزدوج لتحديث النتائج
        self.samples_tree.bind('<Double-1>', self.update_sample_results)
    
    def create_refinements_table(self):
        """إنشاء جدول التنقية"""
        table_frame = tk.Frame(self.parent_frame, bg='white')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        columns = ('ID', 'العينة', 'الوزن الخام', 'الوزن المنقى', 'الخسائر', 'نسبة الاسترداد %', 'المواد المستخدمة', 'التاريخ')
        self.refinements_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=12)
        
        for col in columns:
            self.refinements_tree.heading(col, text=col)
            self.refinements_tree.column(col, width=120, anchor='center')
        
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.refinements_tree.yview)
        self.refinements_tree.configure(yscrollcommand=scrollbar.set)
        
        self.refinements_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_castings_table(self):
        """إنشاء جدول الصب"""
        table_frame = tk.Frame(self.parent_frame, bg='white')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        columns = ('ID', 'العميل', 'وزن السبيكة', 'الشكل', 'نسبة النقاء %', 'الرقم التسلسلي', 'الموظف', 'التاريخ')
        self.castings_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=12)
        
        for col in columns:
            self.castings_tree.heading(col, text=col)
            self.castings_tree.column(col, width=120, anchor='center')
        
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.castings_tree.yview)
        self.castings_tree.configure(yscrollcommand=scrollbar.set)
        
        self.castings_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def load_samples(self):
        """تحميل بيانات العينات"""
        for item in self.samples_tree.get_children():
            self.samples_tree.delete(item)
        
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT s.sample_id, c.name, s.weight, s.material_type, s.purity_percentage, 
                   s.analysis_cost, e.name, s.analysis_date
            FROM Samples s
            LEFT JOIN Customers c ON s.customer_id = c.customer_id
            LEFT JOIN Employees e ON s.employee_id = e.employee_id
            ORDER BY s.analysis_date DESC
        ''')
        samples = cursor.fetchall()
        conn.close()
        
        for sample in samples:
            # تنسيق البيانات
            formatted_sample = list(sample)
            if formatted_sample[4]:  # نسبة النقاء
                formatted_sample[4] = f"{formatted_sample[4]:.2f}%"
            if formatted_sample[5]:  # التكلفة
                formatted_sample[5] = f"{formatted_sample[5]:.2f}"
            
            self.samples_tree.insert('', tk.END, values=formatted_sample)
    
    def load_refinements(self):
        """تحميل بيانات التنقية"""
        for item in self.refinements_tree.get_children():
            self.refinements_tree.delete(item)
        
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT r.refinement_id, r.sample_id, r.raw_weight, r.refined_weight, 
                   r.loss_weight, r.recovery_percentage, r.chemicals_used, r.refinement_date
            FROM Refinements r
            ORDER BY r.refinement_date DESC
        ''')
        refinements = cursor.fetchall()
        conn.close()
        
        for refinement in refinements:
            formatted_refinement = list(refinement)
            if formatted_refinement[5]:  # نسبة الاسترداد
                formatted_refinement[5] = f"{formatted_refinement[5]:.2f}%"
            
            self.refinements_tree.insert('', tk.END, values=formatted_refinement)
    
    def load_castings(self):
        """تحميل بيانات الصب"""
        for item in self.castings_tree.get_children():
            self.castings_tree.delete(item)
        
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT c.casting_id, cust.name, c.bar_weight, c.bar_shape, 
                   c.purity_percentage, c.serial_number, e.name, c.casting_date
            FROM Castings c
            LEFT JOIN Customers cust ON c.customer_id = cust.customer_id
            LEFT JOIN Employees e ON c.employee_id = e.employee_id
            ORDER BY c.casting_date DESC
        ''')
        castings = cursor.fetchall()
        conn.close()
        
        for casting in castings:
            formatted_casting = list(casting)
            if formatted_casting[4]:  # نسبة النقاء
                formatted_casting[4] = f"{formatted_casting[4]:.2f}%"
            
            self.castings_tree.insert('', tk.END, values=formatted_casting)
    
    def show_add_sample_dialog(self):
        """عرض نافذة إضافة عينة جديدة"""
        dialog = tk.Toplevel(self.parent_frame)
        dialog.title("إضافة عينة للتحليل")
        dialog.geometry("500x500")
        dialog.configure(bg='white')
        dialog.resizable(False, False)
        dialog.transient(self.parent_frame)
        dialog.grab_set()
        
        # عنوان النافذة
        title_label = tk.Label(dialog,
                              text="🔬 إضافة عينة للتحليل",
                              font=('Arial', 16, 'bold'),
                              bg='white',
                              fg=self.colors['primary'])
        title_label.pack(pady=20)
        
        # إطار النموذج
        form_frame = tk.Frame(dialog, bg='white')
        form_frame.pack(padx=30, pady=20, fill=tk.BOTH, expand=True)
        
        entries = {}
        
        # اختيار العميل
        tk.Label(form_frame, text="العميل:", font=('Arial', 12), bg='white').grid(row=0, column=0, sticky='w', pady=10)
        customers = self.customer_model.get_all_customers()
        customer_names = [f"{c[1]} - {c[2]}" for c in customers]  # الاسم - رقم الهوية
        entries['customer'] = ttk.Combobox(form_frame, values=customer_names, state='readonly', width=30)
        entries['customer'].grid(row=0, column=1, padx=(10, 0), pady=10)
        
        # الوزن
        tk.Label(form_frame, text="الوزن (غرام):", font=('Arial', 12), bg='white').grid(row=1, column=0, sticky='w', pady=10)
        entries['weight'] = tk.Entry(form_frame, font=('Arial', 12), width=32)
        entries['weight'].grid(row=1, column=1, padx=(10, 0), pady=10)
        
        # نوع المادة
        tk.Label(form_frame, text="نوع المادة:", font=('Arial', 12), bg='white').grid(row=2, column=0, sticky='w', pady=10)
        entries['material_type'] = ttk.Combobox(form_frame, 
                                               values=['ذهب خام', 'سبيكة', 'مجوهرات', 'تراب ذهب'],
                                               state='readonly', width=30)
        entries['material_type'].grid(row=2, column=1, padx=(10, 0), pady=10)
        
        # تكلفة التحليل
        tk.Label(form_frame, text="تكلفة التحليل:", font=('Arial', 12), bg='white').grid(row=3, column=0, sticky='w', pady=10)
        entries['analysis_cost'] = tk.Entry(form_frame, font=('Arial', 12), width=32)
        entries['analysis_cost'].grid(row=3, column=1, padx=(10, 0), pady=10)
        
        # اختيار الموظف
        tk.Label(form_frame, text="الموظف المحلل:", font=('Arial', 12), bg='white').grid(row=4, column=0, sticky='w', pady=10)
        analysts = self.employee_model.get_employees_by_role('محلل')
        analyst_names = [f"{e[1]} - {e[2]}" for e in analysts]
        entries['employee'] = ttk.Combobox(form_frame, values=analyst_names, state='readonly', width=30)
        entries['employee'].grid(row=4, column=1, padx=(10, 0), pady=10)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(pady=20)
        
        save_btn = tk.Button(buttons_frame,
                            text="💾 حفظ",
                            command=lambda: self.save_sample(entries, dialog, customers, analysts),
                            bg=self.colors['success'],
                            fg='white',
                            font=('Arial', 12, 'bold'),
                            relief=tk.FLAT,
                            padx=20,
                            pady=10)
        save_btn.pack(side=tk.LEFT, padx=10)
        
        cancel_btn = tk.Button(buttons_frame,
                              text="❌ إلغاء",
                              command=dialog.destroy,
                              bg=self.colors['danger'],
                              fg='white',
                              font=('Arial', 12, 'bold'),
                              relief=tk.FLAT,
                              padx=20,
                              pady=10)
        cancel_btn.pack(side=tk.LEFT, padx=10)
    
    def save_sample(self, entries, dialog, customers, analysts):
        """حفظ بيانات العينة الجديدة"""
        try:
            customer_index = entries['customer'].current()
            weight = float(entries['weight'].get())
            material_type = entries['material_type'].get()
            analysis_cost = float(entries['analysis_cost'].get())
            employee_index = entries['employee'].current()
            
            if customer_index == -1 or not material_type:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return
            
            customer_id = customers[customer_index][0]
            employee_id = analysts[employee_index][0] if employee_index != -1 else None
            
            sample_id = self.sample_model.add_sample(customer_id, weight, material_type, analysis_cost, employee_id)
            
            messagebox.showinfo("نجح", "تم إضافة العينة بنجاح")
            dialog.destroy()
            self.load_samples()
            
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة للوزن والتكلفة")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")
    
    def update_sample_results(self, event):
        """تحديث نتائج التحليل"""
        selection = self.samples_tree.selection()
        if not selection:
            return
        
        item = self.samples_tree.item(selection[0])
        sample_data = item['values']
        sample_id = sample_data[0]
        
        # نافذة تحديث النتائج
        dialog = tk.Toplevel(self.parent_frame)
        dialog.title("تحديث نتائج التحليل")
        dialog.geometry("450x350")
        dialog.configure(bg='white')
        dialog.resizable(False, False)
        dialog.transient(self.parent_frame)
        dialog.grab_set()
        
        # عنوان النافذة
        title_label = tk.Label(dialog,
                              text="📊 تحديث نتائج التحليل",
                              font=('Arial', 16, 'bold'),
                              bg='white',
                              fg=self.colors['primary'])
        title_label.pack(pady=20)
        
        # إطار النموذج
        form_frame = tk.Frame(dialog, bg='white')
        form_frame.pack(padx=30, pady=20, fill=tk.BOTH, expand=True)
        
        # نسبة النقاء
        tk.Label(form_frame, text="نسبة النقاء (%):", font=('Arial', 12), bg='white').grid(row=0, column=0, sticky='w', pady=10)
        purity_entry = tk.Entry(form_frame, font=('Arial', 12), width=25)
        purity_entry.grid(row=0, column=1, padx=(10, 0), pady=10)
        
        # الشوائب
        tk.Label(form_frame, text="الشوائب:", font=('Arial', 12), bg='white').grid(row=1, column=0, sticky='w', pady=10)
        impurities_entry = tk.Text(form_frame, font=('Arial', 12), width=25, height=3)
        impurities_entry.grid(row=1, column=1, padx=(10, 0), pady=10)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(pady=20)
        
        save_btn = tk.Button(buttons_frame,
                            text="💾 حفظ النتائج",
                            command=lambda: self.save_analysis_results(sample_id, purity_entry, impurities_entry, dialog),
                            bg=self.colors['success'],
                            fg='white',
                            font=('Arial', 12, 'bold'),
                            relief=tk.FLAT,
                            padx=20,
                            pady=10)
        save_btn.pack(side=tk.LEFT, padx=10)
        
        cancel_btn = tk.Button(buttons_frame,
                              text="❌ إلغاء",
                              command=dialog.destroy,
                              bg=self.colors['danger'],
                              fg='white',
                              font=('Arial', 12, 'bold'),
                              relief=tk.FLAT,
                              padx=20,
                              pady=10)
        cancel_btn.pack(side=tk.LEFT, padx=10)
    
    def save_analysis_results(self, sample_id, purity_entry, impurities_entry, dialog):
        """حفظ نتائج التحليل"""
        try:
            purity_percentage = float(purity_entry.get())
            impurities = impurities_entry.get("1.0", tk.END).strip()
            
            self.sample_model.update_analysis_results(sample_id, purity_percentage, impurities)
            
            messagebox.showinfo("نجح", "تم حفظ نتائج التحليل بنجاح")
            dialog.destroy()
            self.load_samples()
            
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيمة صحيحة لنسبة النقاء")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")
    
    def generate_serial_number(self):
        """توليد رقم تسلسلي فريد للسبيكة"""
        timestamp = datetime.now().strftime("%Y%m%d")
        random_part = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
        return f"GOLD-{timestamp}-{random_part}"

    def show_add_refinement_dialog(self):
        """عرض نافذة إضافة عملية تنقية"""
        dialog = tk.Toplevel(self.parent_frame)
        dialog.title("إضافة عملية تنقية")
        dialog.geometry("500x450")
        dialog.configure(bg='white')
        dialog.resizable(False, False)
        dialog.transient(self.parent_frame)
        dialog.grab_set()

        # عنوان النافذة
        title_label = tk.Label(dialog,
                              text="⚗️ إضافة عملية تنقية",
                              font=('Arial', 16, 'bold'),
                              bg='white',
                              fg=self.colors['primary'])
        title_label.pack(pady=20)

        # إطار النموذج
        form_frame = tk.Frame(dialog, bg='white')
        form_frame.pack(padx=30, pady=20, fill=tk.BOTH, expand=True)

        entries = {}

        # اختيار العينة
        tk.Label(form_frame, text="العينة:", font=('Arial', 12), bg='white').grid(row=0, column=0, sticky='w', pady=10)

        # جلب العينات المحللة
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT s.sample_id, c.name, s.weight, s.material_type
            FROM Samples s
            JOIN Customers c ON s.customer_id = c.customer_id
            WHERE s.purity_percentage IS NOT NULL
            ORDER BY s.analysis_date DESC
        ''')
        samples = cursor.fetchall()
        conn.close()

        sample_options = [f"عينة {s[0]} - {s[1]} ({s[2]}غ - {s[3]})" for s in samples]
        entries['sample'] = ttk.Combobox(form_frame, values=sample_options, state='readonly', width=35)
        entries['sample'].grid(row=0, column=1, padx=(10, 0), pady=10)

        # الوزن الخام
        tk.Label(form_frame, text="الوزن الخام (غ):", font=('Arial', 12), bg='white').grid(row=1, column=0, sticky='w', pady=10)
        entries['raw_weight'] = tk.Entry(form_frame, font=('Arial', 12), width=37)
        entries['raw_weight'].grid(row=1, column=1, padx=(10, 0), pady=10)

        # المواد الكيميائية المستخدمة
        tk.Label(form_frame, text="المواد المستخدمة:", font=('Arial', 12), bg='white').grid(row=2, column=0, sticky='w', pady=10)
        entries['chemicals'] = tk.Text(form_frame, font=('Arial', 12), width=37, height=3)
        entries['chemicals'].grid(row=2, column=1, padx=(10, 0), pady=10)

        # اختيار الموظف
        tk.Label(form_frame, text="فني التنقية:", font=('Arial', 12), bg='white').grid(row=3, column=0, sticky='w', pady=10)
        refiners = self.employee_model.get_employees_by_role('فني تنقية')
        refiner_names = [f"{e[1]} - {e[2]}" for e in refiners]
        entries['employee'] = ttk.Combobox(form_frame, values=refiner_names, state='readonly', width=35)
        entries['employee'].grid(row=3, column=1, padx=(10, 0), pady=10)

        # أزرار العمليات
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(pady=20)

        save_btn = tk.Button(buttons_frame,
                            text="💾 حفظ",
                            command=lambda: self.save_refinement(entries, dialog, samples, refiners),
                            bg=self.colors['success'],
                            fg='white',
                            font=('Arial', 12, 'bold'),
                            relief=tk.FLAT,
                            padx=20,
                            pady=10)
        save_btn.pack(side=tk.LEFT, padx=10)

        cancel_btn = tk.Button(buttons_frame,
                              text="❌ إلغاء",
                              command=dialog.destroy,
                              bg=self.colors['danger'],
                              fg='white',
                              font=('Arial', 12, 'bold'),
                              relief=tk.FLAT,
                              padx=20,
                              pady=10)
        cancel_btn.pack(side=tk.LEFT, padx=10)

    def save_refinement(self, entries, dialog, samples, refiners):
        """حفظ بيانات عملية التنقية"""
        try:
            sample_index = entries['sample'].current()
            raw_weight = float(entries['raw_weight'].get())
            chemicals = entries['chemicals'].get("1.0", tk.END).strip()
            employee_index = entries['employee'].current()

            if sample_index == -1:
                messagebox.showerror("خطأ", "يرجى اختيار عينة")
                return

            sample_id = samples[sample_index][0]
            employee_id = refiners[employee_index][0] if employee_index != -1 else None

            refinement_id = self.refinement_model.add_refinement(sample_id, raw_weight, chemicals, employee_id)

            messagebox.showinfo("نجح", "تم إضافة عملية التنقية بنجاح")
            dialog.destroy()
            self.load_refinements()

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيمة صحيحة للوزن")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")

    def show_add_casting_dialog(self):
        """عرض نافذة إضافة عملية صب"""
        dialog = tk.Toplevel(self.parent_frame)
        dialog.title("إضافة عملية صب")
        dialog.geometry("500x500")
        dialog.configure(bg='white')
        dialog.resizable(False, False)
        dialog.transient(self.parent_frame)
        dialog.grab_set()

        # عنوان النافذة
        title_label = tk.Label(dialog,
                              text="🏭 إضافة عملية صب",
                              font=('Arial', 16, 'bold'),
                              bg='white',
                              fg=self.colors['primary'])
        title_label.pack(pady=20)

        # إطار النموذج
        form_frame = tk.Frame(dialog, bg='white')
        form_frame.pack(padx=30, pady=20, fill=tk.BOTH, expand=True)

        entries = {}

        # اختيار العميل
        tk.Label(form_frame, text="العميل:", font=('Arial', 12), bg='white').grid(row=0, column=0, sticky='w', pady=10)
        customers = self.customer_model.get_all_customers()
        customer_names = [f"{c[1]} - {c[2]}" for c in customers]
        entries['customer'] = ttk.Combobox(form_frame, values=customer_names, state='readonly', width=35)
        entries['customer'].grid(row=0, column=1, padx=(10, 0), pady=10)

        # وزن السبيكة
        tk.Label(form_frame, text="وزن السبيكة (غ):", font=('Arial', 12), bg='white').grid(row=1, column=0, sticky='w', pady=10)
        entries['bar_weight'] = tk.Entry(form_frame, font=('Arial', 12), width=37)
        entries['bar_weight'].grid(row=1, column=1, padx=(10, 0), pady=10)

        # شكل السبيكة
        tk.Label(form_frame, text="شكل السبيكة:", font=('Arial', 12), bg='white').grid(row=2, column=0, sticky='w', pady=10)
        entries['bar_shape'] = ttk.Combobox(form_frame,
                                           values=['مستطيل', 'دائري', 'مربع', 'بيضاوي'],
                                           state='readonly', width=35)
        entries['bar_shape'].grid(row=2, column=1, padx=(10, 0), pady=10)

        # نسبة النقاء
        tk.Label(form_frame, text="نسبة النقاء (%):", font=('Arial', 12), bg='white').grid(row=3, column=0, sticky='w', pady=10)
        entries['purity'] = tk.Entry(form_frame, font=('Arial', 12), width=37)
        entries['purity'].grid(row=3, column=1, padx=(10, 0), pady=10)

        # الرقم التسلسلي
        tk.Label(form_frame, text="الرقم التسلسلي:", font=('Arial', 12), bg='white').grid(row=4, column=0, sticky='w', pady=10)
        serial_frame = tk.Frame(form_frame, bg='white')
        serial_frame.grid(row=4, column=1, padx=(10, 0), pady=10, sticky='w')

        entries['serial'] = tk.Entry(serial_frame, font=('Arial', 12), width=25)
        entries['serial'].pack(side=tk.LEFT)

        generate_btn = tk.Button(serial_frame,
                                text="توليد",
                                command=lambda: entries['serial'].delete(0, tk.END) or entries['serial'].insert(0, self.generate_serial_number()),
                                bg=self.colors['secondary'],
                                fg='white',
                                font=('Arial', 8),
                                relief=tk.FLAT)
        generate_btn.pack(side=tk.LEFT, padx=(5, 0))

        # اختيار الموظف
        tk.Label(form_frame, text="فني الصب:", font=('Arial', 12), bg='white').grid(row=5, column=0, sticky='w', pady=10)
        casters = self.employee_model.get_employees_by_role('فني صب')
        caster_names = [f"{e[1]} - {e[2]}" for e in casters]
        entries['employee'] = ttk.Combobox(form_frame, values=caster_names, state='readonly', width=35)
        entries['employee'].grid(row=5, column=1, padx=(10, 0), pady=10)

        # أزرار العمليات
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(pady=20)

        save_btn = tk.Button(buttons_frame,
                            text="💾 حفظ",
                            command=lambda: self.save_casting(entries, dialog, customers, casters),
                            bg=self.colors['success'],
                            fg='white',
                            font=('Arial', 12, 'bold'),
                            relief=tk.FLAT,
                            padx=20,
                            pady=10)
        save_btn.pack(side=tk.LEFT, padx=10)

        cancel_btn = tk.Button(buttons_frame,
                              text="❌ إلغاء",
                              command=dialog.destroy,
                              bg=self.colors['danger'],
                              fg='white',
                              font=('Arial', 12, 'bold'),
                              relief=tk.FLAT,
                              padx=20,
                              pady=10)
        cancel_btn.pack(side=tk.LEFT, padx=10)

    def save_casting(self, entries, dialog, customers, casters):
        """حفظ بيانات عملية الصب"""
        try:
            customer_index = entries['customer'].current()
            bar_weight = float(entries['bar_weight'].get())
            bar_shape = entries['bar_shape'].get()
            purity_percentage = float(entries['purity'].get())
            serial_number = entries['serial'].get().strip()
            employee_index = entries['employee'].current()

            if customer_index == -1 or not bar_shape or not serial_number:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return

            customer_id = customers[customer_index][0]
            employee_id = casters[employee_index][0] if employee_index != -1 else None

            casting_id = self.casting_model.add_casting(customer_id, bar_weight, bar_shape,
                                                       purity_percentage, serial_number, employee_id)

            messagebox.showinfo("نجح", "تم إضافة عملية الصب بنجاح")
            dialog.destroy()
            self.load_castings()

        except ValueError as e:
            if "الرقم التسلسلي موجود مسبقاً" in str(e):
                messagebox.showerror("خطأ", "الرقم التسلسلي موجود مسبقاً")
            else:
                messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة للوزن ونسبة النقاء")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")
