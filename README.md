# برنامج إدارة مختبر تحليل وصب الذهب
## Gold Analysis and Casting Laboratory Management System

برنامج سطح مكتب شامل لإدارة مختبرات تحليل وصب الذهب، مطور بلغة Python باستخدام مكتبة tkinter لواجهة المستخدم.

## 🌟 الميزات الرئيسية

### 📊 لوحة التحكم الرئيسية
- عرض إحصائيات شاملة للمختبر
- بطاقات معلومات تفاعلية
- تصميم عصري وجذاب باللغة العربية

### 👥 إدارة العملاء
- إضافة وتعديل بيانات العملاء
- البحث والتصفية المتقدمة
- عرض تاريخ العمليات لكل عميل

### 👨‍💼 إدارة الموظفين
- تسجيل بيانات الموظفين
- تصنيف حسب الأدوار الوظيفية
- متابعة أداء الموظفين

### 🔬 إدارة التحليل
- تسجيل العينات للتحليل
- تحديث نتائج التحليل ونسب النقاء
- تتبع تكاليف التحليل

### ⚗️ إدارة التنقية
- تسجيل عمليات تنقية الذهب
- متابعة المواد الكيميائية المستخدمة
- حساب نسب الاسترداد

### 🏭 إدارة الصب
- إنتاج السبائك الذهبية
- توليد أرقام تسلسلية فريدة
- تسجيل أشكال وأوزان السبائك

### 📊 التقارير والإحصائيات
- تقارير يومية وأسبوعية وشهرية
- تقارير مخصصة حسب الفترة الزمنية
- تصدير التقارير إلى Excel

### 💰 إدارة الفواتير
- إنشاء فواتير للعملاء
- متابعة المدفوعات
- تحديث حالة الفواتير

## 🛠️ متطلبات التشغيل

### متطلبات النظام
- نظام التشغيل: Windows 10/11, macOS, Linux
- Python 3.7 أو أحدث
- ذاكرة: 4 GB RAM (الحد الأدنى)
- مساحة القرص: 100 MB

### المكتبات المطلوبة
```bash
pip install pandas reportlab xlsxwriter Pillow ttkthemes
```

## 📥 التثبيت والتشغيل

### 1. تحميل الملفات
قم بتحميل جميع ملفات المشروع إلى مجلد واحد:
- `run_app.py` - ملف تشغيل التطبيق
- `database.py` - إدارة قاعدة البيانات
- `models.py` - نماذج البيانات
- `main_window.py` - النافذة الرئيسية
- `customer_management.py` - إدارة العملاء
- `employee_management.py` - إدارة الموظفين
- `operations_management.py` - إدارة العمليات
- `reports_invoices.py` - التقارير والفواتير
- `requirements.txt` - قائمة المتطلبات

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل التطبيق
```bash
python run_app.py
```

## 🗄️ قاعدة البيانات

يستخدم التطبيق قاعدة بيانات SQLite محلية تحتوي على الجداول التالية:

- **Customers**: بيانات العملاء
- **Employees**: بيانات الموظفين
- **Samples**: عينات التحليل
- **Refinements**: عمليات التنقية
- **Castings**: عمليات الصب
- **Invoices**: الفواتير
- **Reports**: التقارير

## 🎨 واجهة المستخدم

### التصميم
- واجهة عربية بالكامل
- ألوان عصرية ومريحة للعين
- تخطيط منظم وسهل الاستخدام
- أيقونات تعبيرية (إيموجي) لسهولة التنقل

### الألوان المستخدمة
- الأزرق الداكن: العناصر الرئيسية
- الأخضر: العمليات الناجحة
- البرتقالي: التحذيرات
- الأحمر: الأخطاء والحذف
- الذهبي: عمليات الذهب

## 📋 دليل الاستخدام

### البدء السريع
1. شغل التطبيق باستخدام `python run_app.py`
2. ستظهر لوحة التحكم الرئيسية
3. استخدم القائمة الجانبية للتنقل بين الأقسام
4. ابدأ بإضافة العملاء والموظفين
5. سجل العينات للتحليل
6. أنتج التقارير والفواتير

### إضافة عميل جديد
1. اذهب إلى قسم "إدارة العملاء"
2. اضغط على "إضافة عميل جديد"
3. املأ البيانات المطلوبة
4. اضغط "حفظ"

### تسجيل عينة للتحليل
1. اذهب إلى قسم "التحليل"
2. اضغط على "إضافة عينة للتحليل"
3. اختر العميل والموظف المحلل
4. أدخل بيانات العينة
5. احفظ البيانات

### إنتاج تقرير
1. اذهب إلى قسم "التقارير"
2. اختر نوع التقرير (يومي/أسبوعي/شهري/مخصص)
3. اعرض التقرير
4. صدر إلى Excel إذا أردت

## 🔧 الصيانة والدعم

### النسخ الاحتياطي
- يُنصح بعمل نسخة احتياطية من ملف `gold_lab.db` بانتظام
- يمكن نسخ المجلد كاملاً كنسخة احتياطية

### استكشاف الأخطاء
- تأكد من تثبيت جميع المتطلبات
- تحقق من صلاحيات الكتابة في مجلد التطبيق
- راجع رسائل الخطأ في وحدة التحكم

## 📞 الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:
- تحقق من رسائل الخطأ في وحدة التحكم
- تأكد من تحديث Python والمكتبات
- راجع ملف README للحلول الشائعة

## 📄 الترخيص

هذا البرنامج مطور لأغراض تعليمية وتجارية. يمكن استخدامه وتعديله حسب الحاجة.

## 🏆 المطور

تم تطوير هذا البرنامج بواسطة **Augment Agent** باستخدام أحدث تقنيات البرمجة وأفضل الممارسات في تطوير تطبيقات سطح المكتب.

---

**ملاحظة**: هذا البرنامج مصمم خصيصاً لمختبرات تحليل وصب الذهب ويمكن تخصيصه حسب احتياجات كل مختبر.
