import sqlite3
import os
from datetime import datetime

class DatabaseManager:
    def __init__(self, db_name="gold_lab.db"):
        self.db_name = db_name
        self.init_database()
    
    def get_connection(self):
        """إنشاء اتصال بقاعدة البيانات"""
        return sqlite3.connect(self.db_name)
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول العملاء
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS Customers (
                customer_id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                id_number TEXT UNIQUE NOT NULL,
                phone TEXT,
                address TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الموظفين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS Employees (
                employee_id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                id_number TEXT UNIQUE NOT NULL,
                role TEXT NOT NULL,
                phone TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول العينات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS Samples (
                sample_id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER NOT NULL,
                weight REAL NOT NULL,
                material_type TEXT NOT NULL,
                purity_percentage REAL,
                impurities TEXT,
                analysis_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                analysis_cost REAL,
                employee_id INTEGER,
                FOREIGN KEY (customer_id) REFERENCES Customers(customer_id),
                FOREIGN KEY (employee_id) REFERENCES Employees(employee_id)
            )
        ''')
        
        # جدول التنقية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS Refinements (
                refinement_id INTEGER PRIMARY KEY AUTOINCREMENT,
                sample_id INTEGER NOT NULL,
                raw_weight REAL NOT NULL,
                refined_weight REAL,
                loss_weight REAL,
                chemicals_used TEXT,
                refinement_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                recovery_percentage REAL,
                employee_id INTEGER,
                FOREIGN KEY (sample_id) REFERENCES Samples(sample_id),
                FOREIGN KEY (employee_id) REFERENCES Employees(employee_id)
            )
        ''')
        
        # جدول الصب
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS Castings (
                casting_id INTEGER PRIMARY KEY AUTOINCREMENT,
                refinement_id INTEGER,
                customer_id INTEGER NOT NULL,
                bar_weight REAL NOT NULL,
                bar_shape TEXT NOT NULL,
                purity_percentage REAL NOT NULL,
                serial_number TEXT UNIQUE NOT NULL,
                casting_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                employee_id INTEGER,
                FOREIGN KEY (refinement_id) REFERENCES Refinements(refinement_id),
                FOREIGN KEY (customer_id) REFERENCES Customers(customer_id),
                FOREIGN KEY (employee_id) REFERENCES Employees(employee_id)
            )
        ''')
        
        # جدول الفواتير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS Invoices (
                invoice_id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER NOT NULL,
                sample_id INTEGER,
                refinement_id INTEGER,
                casting_id INTEGER,
                total_amount REAL NOT NULL,
                paid_amount REAL DEFAULT 0,
                status TEXT NOT NULL,
                invoice_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES Customers(customer_id),
                FOREIGN KEY (sample_id) REFERENCES Samples(sample_id),
                FOREIGN KEY (refinement_id) REFERENCES Refinements(refinement_id),
                FOREIGN KEY (casting_id) REFERENCES Castings(casting_id)
            )
        ''')
        
        # جدول التقارير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS Reports (
                report_id INTEGER PRIMARY KEY AUTOINCREMENT,
                report_type TEXT NOT NULL,
                start_date TIMESTAMP NOT NULL,
                end_date TIMESTAMP NOT NULL,
                total_samples INTEGER,
                total_casted_weight REAL,
                total_revenue REAL,
                generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        print("تم إنشاء قاعدة البيانات بنجاح!")

if __name__ == "__main__":
    db = DatabaseManager()
