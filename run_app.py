#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج إدارة مختبر تحليل وصب الذهب
Gold Analysis and Casting Laboratory Management System

تطوير: Augment Agent
التاريخ: 2025-07-01
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from database import DatabaseManager
    from main_window import GoldLabApp
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    print("تأكد من وجود جميع الملفات المطلوبة في نفس المجلد")
    sys.exit(1)

def check_dependencies():
    """فحص التبعيات المطلوبة"""
    required_modules = ['pandas', 'reportlab', 'xlsxwriter', 'PIL']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        error_msg = f"""
        الوحدات التالية مفقودة:
        {', '.join(missing_modules)}
        
        يرجى تثبيتها باستخدام الأمر:
        pip install {' '.join(missing_modules)}
        """
        messagebox.showerror("وحدات مفقودة", error_msg)
        return False
    
    return True

def initialize_database():
    """تهيئة قاعدة البيانات"""
    try:
        db_manager = DatabaseManager()
        db_manager.create_tables()
        return db_manager
    except Exception as e:
        messagebox.showerror("خطأ في قاعدة البيانات", f"فشل في تهيئة قاعدة البيانات:\n{str(e)}")
        return None

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    print("🚀 بدء تشغيل برنامج إدارة مختبر الذهب...")
    
    # فحص التبعيات
    if not check_dependencies():
        return
    
    # تهيئة قاعدة البيانات
    db_manager = initialize_database()
    if not db_manager:
        return
    
    try:
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        
        # إعداد النافذة
        root.title("برنامج إدارة مختبر تحليل وصب الذهب")
        root.geometry("1200x800")
        root.minsize(1000, 600)
        
        # تعيين أيقونة التطبيق (إذا كانت متوفرة)
        try:
            root.iconbitmap("icon.ico")
        except:
            pass  # تجاهل إذا لم تكن الأيقونة متوفرة
        
        # إنشاء التطبيق
        app = GoldLabApp(root, db_manager)
        
        # معالج إغلاق التطبيق
        def on_closing():
            if messagebox.askokcancel("إغلاق التطبيق", "هل تريد إغلاق التطبيق؟"):
                try:
                    db_manager.close()
                except:
                    pass
                root.destroy()
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        
        print("✅ تم تشغيل التطبيق بنجاح!")
        print("📋 الميزات المتاحة:")
        print("   • إدارة العملاء والموظفين")
        print("   • إدارة عمليات التحليل والتنقية والصب")
        print("   • إنتاج التقارير والفواتير")
        print("   • قاعدة بيانات شاملة")
        
        # تشغيل التطبيق
        root.mainloop()
        
    except Exception as e:
        messagebox.showerror("خطأ في التطبيق", f"حدث خطأ غير متوقع:\n{str(e)}")
        print(f"❌ خطأ في التطبيق: {e}")
    
    finally:
        # تنظيف الموارد
        try:
            if db_manager:
                db_manager.close()
        except:
            pass
        print("🔚 تم إغلاق التطبيق")

if __name__ == "__main__":
    main()
