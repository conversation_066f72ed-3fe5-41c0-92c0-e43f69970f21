import tkinter as tk
from tkinter import ttk, messagebox
from models import Employee

class EmployeeManagement:
    def __init__(self, parent_frame, db_manager, colors):
        self.parent_frame = parent_frame
        self.db_manager = db_manager
        self.employee_model = Employee(db_manager)
        self.colors = colors
        self.selected_employee = None
        
    def show_employee_management(self):
        """عرض واجهة إدارة الموظفين"""
        # إطار العنوان
        title_frame = tk.Frame(self.parent_frame, bg='white')
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        
        title_label = tk.Label(title_frame,
                              text="👨‍💼 إدارة الموظفين",
                              font=('Arial', 18, 'bold'),
                              bg='white',
                              fg=self.colors['primary'])
        title_label.pack(side=tk.LEFT)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(title_frame, bg='white')
        buttons_frame.pack(side=tk.RIGHT)
        
        add_btn = tk.Button(buttons_frame,
                           text="➕ إضافة موظف",
                           command=self.show_add_employee_dialog,
                           bg=self.colors['success'],
                           fg='white',
                           font=('Arial', 10, 'bold'),
                           relief=tk.FLAT,
                           padx=15,
                           pady=5)
        add_btn.pack(side=tk.LEFT, padx=5)
        
        edit_btn = tk.Button(buttons_frame,
                            text="✏️ تعديل",
                            command=self.show_edit_employee_dialog,
                            bg=self.colors['warning'],
                            fg='white',
                            font=('Arial', 10, 'bold'),
                            relief=tk.FLAT,
                            padx=15,
                            pady=5)
        edit_btn.pack(side=tk.LEFT, padx=5)
        
        # إطار الفلترة حسب الوظيفة
        filter_frame = tk.Frame(self.parent_frame, bg='white')
        filter_frame.pack(fill=tk.X, padx=20, pady=10)
        
        filter_label = tk.Label(filter_frame,
                               text="🔍 فلترة حسب الوظيفة:",
                               font=('Arial', 12),
                               bg='white')
        filter_label.pack(side=tk.LEFT, padx=(0, 10))
        
        self.role_var = tk.StringVar()
        role_combo = ttk.Combobox(filter_frame,
                                 textvariable=self.role_var,
                                 values=['الكل', 'محلل', 'فني تنقية', 'فني صب', 'مدير', 'محاسب'],
                                 state='readonly',
                                 width=20)
        role_combo.pack(side=tk.LEFT, padx=(0, 10))
        role_combo.set('الكل')
        role_combo.bind('<<ComboboxSelected>>', self.filter_employees)
        
        # جدول الموظفين
        self.create_employees_table()
        self.load_employees()
    
    def create_employees_table(self):
        """إنشاء جدول الموظفين"""
        table_frame = tk.Frame(self.parent_frame, bg='white')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # إنشاء Treeview
        columns = ('ID', 'الاسم', 'رقم الهوية', 'الوظيفة', 'الهاتف', 'تاريخ التسجيل')
        self.employees_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.employees_tree.heading(col, text=col)
            self.employees_tree.column(col, width=150, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.employees_tree.yview)
        self.employees_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.employees_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط حدث النقر
        self.employees_tree.bind('<ButtonRelease-1>', self.on_employee_select)
    
    def load_employees(self):
        """تحميل بيانات الموظفين"""
        # مسح البيانات الحالية
        for item in self.employees_tree.get_children():
            self.employees_tree.delete(item)
        
        # جلب الموظفين من قاعدة البيانات
        employees = self.employee_model.get_all_employees()
        
        for employee in employees:
            self.employees_tree.insert('', tk.END, values=employee)
    
    def filter_employees(self, event=None):
        """فلترة الموظفين حسب الوظيفة"""
        selected_role = self.role_var.get()
        
        # مسح البيانات الحالية
        for item in self.employees_tree.get_children():
            self.employees_tree.delete(item)
        
        if selected_role == 'الكل':
            employees = self.employee_model.get_all_employees()
        else:
            employees = self.employee_model.get_employees_by_role(selected_role)
        
        for employee in employees:
            self.employees_tree.insert('', tk.END, values=employee)
    
    def on_employee_select(self, event):
        """عند اختيار موظف من الجدول"""
        selection = self.employees_tree.selection()
        if selection:
            item = self.employees_tree.item(selection[0])
            self.selected_employee = item['values']
    
    def show_add_employee_dialog(self):
        """عرض نافذة إضافة موظف جديد"""
        dialog = tk.Toplevel(self.parent_frame)
        dialog.title("إضافة موظف جديد")
        dialog.geometry("450x450")
        dialog.configure(bg='white')
        dialog.resizable(False, False)
        
        # جعل النافذة في المقدمة
        dialog.transient(self.parent_frame)
        dialog.grab_set()
        
        # عنوان النافذة
        title_label = tk.Label(dialog,
                              text="➕ إضافة موظف جديد",
                              font=('Arial', 16, 'bold'),
                              bg='white',
                              fg=self.colors['primary'])
        title_label.pack(pady=20)
        
        # إطار النموذج
        form_frame = tk.Frame(dialog, bg='white')
        form_frame.pack(padx=30, pady=20, fill=tk.BOTH, expand=True)
        
        # حقول الإدخال
        entries = {}
        
        # الاسم
        tk.Label(form_frame, text="الاسم الكامل:", font=('Arial', 12), bg='white').grid(row=0, column=0, sticky='w', pady=10)
        entries['name'] = tk.Entry(form_frame, font=('Arial', 12), width=25)
        entries['name'].grid(row=0, column=1, padx=(10, 0), pady=10)
        
        # رقم الهوية
        tk.Label(form_frame, text="رقم الهوية:", font=('Arial', 12), bg='white').grid(row=1, column=0, sticky='w', pady=10)
        entries['id_number'] = tk.Entry(form_frame, font=('Arial', 12), width=25)
        entries['id_number'].grid(row=1, column=1, padx=(10, 0), pady=10)
        
        # الوظيفة
        tk.Label(form_frame, text="الوظيفة:", font=('Arial', 12), bg='white').grid(row=2, column=0, sticky='w', pady=10)
        entries['role'] = ttk.Combobox(form_frame, 
                                      values=['محلل', 'فني تنقية', 'فني صب', 'مدير', 'محاسب'],
                                      state='readonly',
                                      width=22)
        entries['role'].grid(row=2, column=1, padx=(10, 0), pady=10)
        
        # رقم الهاتف
        tk.Label(form_frame, text="رقم الهاتف:", font=('Arial', 12), bg='white').grid(row=3, column=0, sticky='w', pady=10)
        entries['phone'] = tk.Entry(form_frame, font=('Arial', 12), width=25)
        entries['phone'].grid(row=3, column=1, padx=(10, 0), pady=10)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(pady=20)
        
        save_btn = tk.Button(buttons_frame,
                            text="💾 حفظ",
                            command=lambda: self.save_employee(entries, dialog),
                            bg=self.colors['success'],
                            fg='white',
                            font=('Arial', 12, 'bold'),
                            relief=tk.FLAT,
                            padx=20,
                            pady=10)
        save_btn.pack(side=tk.LEFT, padx=10)
        
        cancel_btn = tk.Button(buttons_frame,
                              text="❌ إلغاء",
                              command=dialog.destroy,
                              bg=self.colors['danger'],
                              fg='white',
                              font=('Arial', 12, 'bold'),
                              relief=tk.FLAT,
                              padx=20,
                              pady=10)
        cancel_btn.pack(side=tk.LEFT, padx=10)
    
    def save_employee(self, entries, dialog):
        """حفظ بيانات الموظف الجديد"""
        try:
            name = entries['name'].get().strip()
            id_number = entries['id_number'].get().strip()
            role = entries['role'].get()
            phone = entries['phone'].get().strip()
            
            # التحقق من البيانات المطلوبة
            if not name or not id_number or not role:
                messagebox.showerror("خطأ", "الاسم ورقم الهوية والوظيفة مطلوبة")
                return
            
            # إضافة الموظف
            employee_id = self.employee_model.add_employee(name, id_number, role, phone)
            
            messagebox.showinfo("نجح", "تم إضافة الموظف بنجاح")
            dialog.destroy()
            self.load_employees()  # إعادة تحميل البيانات
            
        except ValueError as e:
            messagebox.showerror("خطأ", str(e))
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")
    
    def show_edit_employee_dialog(self):
        """عرض نافذة تعديل الموظف المحدد"""
        if not self.selected_employee:
            messagebox.showwarning("تحذير", "يرجى اختيار موظف للتعديل")
            return
        
        dialog = tk.Toplevel(self.parent_frame)
        dialog.title("تعديل بيانات الموظف")
        dialog.geometry("450x450")
        dialog.configure(bg='white')
        dialog.resizable(False, False)
        
        # جعل النافذة في المقدمة
        dialog.transient(self.parent_frame)
        dialog.grab_set()
        
        # عنوان النافذة
        title_label = tk.Label(dialog,
                              text="✏️ تعديل بيانات الموظف",
                              font=('Arial', 16, 'bold'),
                              bg='white',
                              fg=self.colors['primary'])
        title_label.pack(pady=20)
        
        # إطار النموذج
        form_frame = tk.Frame(dialog, bg='white')
        form_frame.pack(padx=30, pady=20, fill=tk.BOTH, expand=True)
        
        # حقول الإدخال مع البيانات الحالية
        entries = {}
        
        # الاسم
        tk.Label(form_frame, text="الاسم الكامل:", font=('Arial', 12), bg='white').grid(row=0, column=0, sticky='w', pady=10)
        entries['name'] = tk.Entry(form_frame, font=('Arial', 12), width=25)
        entries['name'].grid(row=0, column=1, padx=(10, 0), pady=10)
        entries['name'].insert(0, self.selected_employee[1] or "")
        
        # الوظيفة
        tk.Label(form_frame, text="الوظيفة:", font=('Arial', 12), bg='white').grid(row=1, column=0, sticky='w', pady=10)
        entries['role'] = ttk.Combobox(form_frame, 
                                      values=['محلل', 'فني تنقية', 'فني صب', 'مدير', 'محاسب'],
                                      state='readonly',
                                      width=22)
        entries['role'].grid(row=1, column=1, padx=(10, 0), pady=10)
        entries['role'].set(self.selected_employee[3] or "")
        
        # رقم الهاتف
        tk.Label(form_frame, text="رقم الهاتف:", font=('Arial', 12), bg='white').grid(row=2, column=0, sticky='w', pady=10)
        entries['phone'] = tk.Entry(form_frame, font=('Arial', 12), width=25)
        entries['phone'].grid(row=2, column=1, padx=(10, 0), pady=10)
        entries['phone'].insert(0, self.selected_employee[4] or "")
        
        # أزرار العمليات
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(pady=20)
        
        save_btn = tk.Button(buttons_frame,
                            text="💾 حفظ التغييرات",
                            command=lambda: self.update_employee(entries, dialog),
                            bg=self.colors['success'],
                            fg='white',
                            font=('Arial', 12, 'bold'),
                            relief=tk.FLAT,
                            padx=20,
                            pady=10)
        save_btn.pack(side=tk.LEFT, padx=10)
        
        cancel_btn = tk.Button(buttons_frame,
                              text="❌ إلغاء",
                              command=dialog.destroy,
                              bg=self.colors['danger'],
                              fg='white',
                              font=('Arial', 12, 'bold'),
                              relief=tk.FLAT,
                              padx=20,
                              pady=10)
        cancel_btn.pack(side=tk.LEFT, padx=10)
    
    def update_employee(self, entries, dialog):
        """تحديث بيانات الموظف"""
        try:
            employee_id = self.selected_employee[0]
            name = entries['name'].get().strip()
            role = entries['role'].get()
            phone = entries['phone'].get().strip()
            
            # التحقق من البيانات المطلوبة
            if not name or not role:
                messagebox.showerror("خطأ", "الاسم والوظيفة مطلوبان")
                return
            
            # تحديث الموظف (نحتاج لإضافة هذه الدالة في models.py)
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE Employees 
                SET name=?, role=?, phone=?
                WHERE employee_id=?
            ''', (name, role, phone, employee_id))
            conn.commit()
            conn.close()
            
            messagebox.showinfo("نجح", "تم تحديث بيانات الموظف بنجاح")
            dialog.destroy()
            self.load_employees()  # إعادة تحميل البيانات
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")
