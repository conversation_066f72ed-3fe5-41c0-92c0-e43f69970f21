import tkinter as tk
from tkinter import ttk, messagebox
from models import Customer

class CustomerManagement:
    def __init__(self, parent_frame, db_manager, colors):
        self.parent_frame = parent_frame
        self.db_manager = db_manager
        self.customer_model = Customer(db_manager)
        self.colors = colors
        self.selected_customer = None
        
    def show_customer_management(self):
        """عرض واجهة إدارة العملاء"""
        # إطار العنوان
        title_frame = tk.Frame(self.parent_frame, bg='white')
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        
        title_label = tk.Label(title_frame,
                              text="👥 إدارة العملاء",
                              font=('Arial', 18, 'bold'),
                              bg='white',
                              fg=self.colors['primary'])
        title_label.pack(side=tk.LEFT)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(title_frame, bg='white')
        buttons_frame.pack(side=tk.RIGHT)
        
        add_btn = tk.Button(buttons_frame,
                           text="➕ إضافة عميل",
                           command=self.show_add_customer_dialog,
                           bg=self.colors['success'],
                           fg='white',
                           font=('Arial', 10, 'bold'),
                           relief=tk.FLAT,
                           padx=15,
                           pady=5)
        add_btn.pack(side=tk.LEFT, padx=5)
        
        edit_btn = tk.Button(buttons_frame,
                            text="✏️ تعديل",
                            command=self.show_edit_customer_dialog,
                            bg=self.colors['warning'],
                            fg='white',
                            font=('Arial', 10, 'bold'),
                            relief=tk.FLAT,
                            padx=15,
                            pady=5)
        edit_btn.pack(side=tk.LEFT, padx=5)
        
        # إطار البحث
        search_frame = tk.Frame(self.parent_frame, bg='white')
        search_frame.pack(fill=tk.X, padx=20, pady=10)
        
        search_label = tk.Label(search_frame,
                               text="🔍 البحث:",
                               font=('Arial', 12),
                               bg='white')
        search_label.pack(side=tk.LEFT, padx=(0, 10))
        
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame,
                               textvariable=self.search_var,
                               font=('Arial', 12),
                               width=30)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.search_customers)
        
        search_btn = tk.Button(search_frame,
                              text="بحث",
                              command=self.search_customers,
                              bg=self.colors['secondary'],
                              fg='white',
                              font=('Arial', 10),
                              relief=tk.FLAT,
                              padx=10)
        search_btn.pack(side=tk.LEFT)
        
        # جدول العملاء
        self.create_customers_table()
        self.load_customers()
    
    def create_customers_table(self):
        """إنشاء جدول العملاء"""
        table_frame = tk.Frame(self.parent_frame, bg='white')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # إنشاء Treeview
        columns = ('ID', 'الاسم', 'رقم الهوية', 'الهاتف', 'العنوان', 'تاريخ التسجيل')
        self.customers_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.customers_tree.heading(col, text=col)
            self.customers_tree.column(col, width=150, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.customers_tree.yview)
        self.customers_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.customers_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط حدث النقر
        self.customers_tree.bind('<ButtonRelease-1>', self.on_customer_select)
    
    def load_customers(self):
        """تحميل بيانات العملاء"""
        # مسح البيانات الحالية
        for item in self.customers_tree.get_children():
            self.customers_tree.delete(item)
        
        # جلب العملاء من قاعدة البيانات
        customers = self.customer_model.get_all_customers()
        
        for customer in customers:
            self.customers_tree.insert('', tk.END, values=customer)
    
    def search_customers(self, event=None):
        """البحث عن العملاء"""
        search_term = self.search_var.get()
        
        # مسح البيانات الحالية
        for item in self.customers_tree.get_children():
            self.customers_tree.delete(item)
        
        if search_term:
            customers = self.customer_model.search_customer(search_term)
        else:
            customers = self.customer_model.get_all_customers()
        
        for customer in customers:
            self.customers_tree.insert('', tk.END, values=customer)
    
    def on_customer_select(self, event):
        """عند اختيار عميل من الجدول"""
        selection = self.customers_tree.selection()
        if selection:
            item = self.customers_tree.item(selection[0])
            self.selected_customer = item['values']
    
    def show_add_customer_dialog(self):
        """عرض نافذة إضافة عميل جديد"""
        dialog = tk.Toplevel(self.parent_frame)
        dialog.title("إضافة عميل جديد")
        dialog.geometry("450x400")
        dialog.configure(bg='white')
        dialog.resizable(False, False)
        
        # جعل النافذة في المقدمة
        dialog.transient(self.parent_frame)
        dialog.grab_set()
        
        # عنوان النافذة
        title_label = tk.Label(dialog,
                              text="➕ إضافة عميل جديد",
                              font=('Arial', 16, 'bold'),
                              bg='white',
                              fg=self.colors['primary'])
        title_label.pack(pady=20)
        
        # إطار النموذج
        form_frame = tk.Frame(dialog, bg='white')
        form_frame.pack(padx=30, pady=20, fill=tk.BOTH, expand=True)
        
        # حقول الإدخال
        fields = [
            ("الاسم الكامل:", "name"),
            ("رقم الهوية:", "id_number"),
            ("رقم الهاتف:", "phone"),
            ("العنوان:", "address")
        ]
        
        entries = {}
        for i, (label_text, field_name) in enumerate(fields):
            label = tk.Label(form_frame,
                            text=label_text,
                            font=('Arial', 12),
                            bg='white')
            label.grid(row=i, column=0, sticky='w', pady=10)
            
            entry = tk.Entry(form_frame,
                            font=('Arial', 12),
                            width=25)
            entry.grid(row=i, column=1, padx=(10, 0), pady=10)
            entries[field_name] = entry
        
        # أزرار العمليات
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(pady=30, side=tk.BOTTOM)
        
        save_btn = tk.Button(buttons_frame,
                            text="💾 حفظ",
                            command=lambda: self.save_customer(entries, dialog),
                            bg=self.colors['success'],
                            fg='white',
                            font=('Arial', 12, 'bold'),
                            relief=tk.FLAT,
                            padx=20,
                            pady=10)
        save_btn.pack(side=tk.LEFT, padx=10)
        
        cancel_btn = tk.Button(buttons_frame,
                              text="❌ إلغاء",
                              command=dialog.destroy,
                              bg=self.colors['danger'],
                              fg='white',
                              font=('Arial', 12, 'bold'),
                              relief=tk.FLAT,
                              padx=20,
                              pady=10)
        cancel_btn.pack(side=tk.LEFT, padx=10)
    
    def save_customer(self, entries, dialog):
        """حفظ بيانات العميل الجديد"""
        try:
            name = entries['name'].get().strip()
            id_number = entries['id_number'].get().strip()
            phone = entries['phone'].get().strip()
            address = entries['address'].get().strip()
            
            # التحقق من البيانات المطلوبة
            if not name or not id_number:
                messagebox.showerror("خطأ", "الاسم ورقم الهوية مطلوبان")
                return
            
            # إضافة العميل
            customer_id = self.customer_model.add_customer(name, id_number, phone, address)
            
            messagebox.showinfo("نجح", "تم إضافة العميل بنجاح")
            dialog.destroy()
            self.load_customers()  # إعادة تحميل البيانات
            
        except ValueError as e:
            messagebox.showerror("خطأ", str(e))
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")
    
    def show_edit_customer_dialog(self):
        """عرض نافذة تعديل العميل المحدد"""
        if not self.selected_customer:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للتعديل")
            return
        
        dialog = tk.Toplevel(self.parent_frame)
        dialog.title("تعديل بيانات العميل")
        dialog.geometry("450x400")
        dialog.configure(bg='white')
        dialog.resizable(False, False)
        
        # جعل النافذة في المقدمة
        dialog.transient(self.parent_frame)
        dialog.grab_set()
        
        # عنوان النافذة
        title_label = tk.Label(dialog,
                              text="✏️ تعديل بيانات العميل",
                              font=('Arial', 16, 'bold'),
                              bg='white',
                              fg=self.colors['primary'])
        title_label.pack(pady=20)
        
        # إطار النموذج
        form_frame = tk.Frame(dialog, bg='white')
        form_frame.pack(padx=30, pady=20, fill=tk.BOTH, expand=True)
        
        # حقول الإدخال مع البيانات الحالية
        fields = [
            ("الاسم الكامل:", "name", self.selected_customer[1]),
            ("رقم الهاتف:", "phone", self.selected_customer[3]),
            ("العنوان:", "address", self.selected_customer[4])
        ]
        
        entries = {}
        for i, (label_text, field_name, current_value) in enumerate(fields):
            label = tk.Label(form_frame,
                            text=label_text,
                            font=('Arial', 12),
                            bg='white')
            label.grid(row=i, column=0, sticky='w', pady=10)
            
            entry = tk.Entry(form_frame,
                            font=('Arial', 12),
                            width=25)
            entry.grid(row=i, column=1, padx=(10, 0), pady=10)
            entry.insert(0, current_value or "")
            entries[field_name] = entry
        
        # أزرار العمليات
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(pady=20)
        
        save_btn = tk.Button(buttons_frame,
                            text="💾 حفظ التغييرات",
                            command=lambda: self.update_customer(entries, dialog),
                            bg=self.colors['success'],
                            fg='white',
                            font=('Arial', 12, 'bold'),
                            relief=tk.FLAT,
                            padx=20,
                            pady=10)
        save_btn.pack(side=tk.LEFT, padx=10)
        
        cancel_btn = tk.Button(buttons_frame,
                              text="❌ إلغاء",
                              command=dialog.destroy,
                              bg=self.colors['danger'],
                              fg='white',
                              font=('Arial', 12, 'bold'),
                              relief=tk.FLAT,
                              padx=20,
                              pady=10)
        cancel_btn.pack(side=tk.LEFT, padx=10)
    
    def update_customer(self, entries, dialog):
        """تحديث بيانات العميل"""
        try:
            customer_id = self.selected_customer[0]
            name = entries['name'].get().strip()
            phone = entries['phone'].get().strip()
            address = entries['address'].get().strip()
            
            # التحقق من البيانات المطلوبة
            if not name:
                messagebox.showerror("خطأ", "الاسم مطلوب")
                return
            
            # تحديث العميل
            self.customer_model.update_customer(customer_id, name, phone, address)
            
            messagebox.showinfo("نجح", "تم تحديث بيانات العميل بنجاح")
            dialog.destroy()
            self.load_customers()  # إعادة تحميل البيانات
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")
